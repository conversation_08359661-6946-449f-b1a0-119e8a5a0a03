"use client"

import React, { useState, useEffect, useRef } from "react";

export const ScrollSpyTabs = ({ sectionIds }: { sectionIds: { id: string, label: string }[] }) => {
  const [active, setActive] = useState(sectionIds[0].id)
  const tabsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const visible = entries.find(entry => entry.isIntersecting)
        if (visible) {
          setActive(visible.target.id)
          scrollToActiveTab(visible.target.id)
        }
      },
      { threshold: 0.1 }
    )

    sectionIds.forEach(sec => {
      const el = document.getElementById(sec.id)
      if (el) observer.observe(el)
    })

    return () => observer.disconnect()
  }, [sectionIds])

  const scrollToActiveTab = (activeId: string) => {
    if (!tabsRef.current) return
    
    const activeButton = tabsRef.current.querySelector(`[data-tab-id="${activeId}"]`) as HTMLElement
    if (!activeButton) return

    const container = tabsRef.current
    const containerRect = container.getBoundingClientRect()
    const buttonRect = activeButton.getBoundingClientRect()

    const isLeftOutside = buttonRect.left < containerRect.left
    const isRightOutside = buttonRect.right > containerRect.right

    if (isLeftOutside || isRightOutside) {
      const containerWidth = container.clientWidth
      const buttonWidth = activeButton.offsetWidth
      const buttonOffsetLeft = activeButton.offsetLeft
      
      const scrollPosition = buttonOffsetLeft - (containerWidth / 2) + (buttonWidth / 2)
      
      container.scrollTo({
        left: Math.max(0, scrollPosition),
        behavior: 'smooth'
      })
    }
  }

  const scrollTo = (id: string) => {
    document.getElementById(id)?.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }

  return (
    <div className="sticky top-32 md:top-31 z-29 bg-white border-b mb-5">
      <div
        ref={tabsRef}
        className="flex flex-nowrap space-x-1 md:space-x-4 overflow-x-scroll scroll-smooth px-4 py-2"
      >
        {sectionIds.map(sec => (
          <button
            key={sec.id}
            data-tab-id={sec.id}
            onClick={() => scrollTo(sec.id)}
            className={`px-3 py-1 rounded-md whitespace-nowrap transition ${active === sec.id ? 'bg-brand text-white' : 'text-gray-600 hover:bg-secondary'
              }`}
          >
            {sec.label}
          </button>
        ))}
      </div>
    </div>
  )
}