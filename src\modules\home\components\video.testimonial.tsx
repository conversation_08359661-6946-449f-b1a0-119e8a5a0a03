'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Play,
  Calendar,
  Facebook,
  X,
  Pin,
  Mail as MailIcon,
} from 'lucide-react';
import { IHomeVideoTestimonial } from '@/types/home';

export default function VideoTestimonial({
  testimonial,
}: {
  testimonial: IHomeVideoTestimonial;
}) {
  const [openId, setOpenId] = useState<string | null>(null);

  // close on ESC
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpenId(null);
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, []);

  return (
    <>
      <section className="mb-24 md:mb-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="md:text-4xl text-3xl font-bold text-dark tracking-wide mb-2">
            {testimonial.title}
          </h2>
          <p className="text-lg text-gray-600 mb-12">{testimonial.subtitle}</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonial.testimonials.map((t) => (
              <div
                key={t.id}
                className="relative bg-white border border-gray-200 rounded-lg overflow-hidden group"
              >
                <div className="relative w-full h-64">
                  <Image
                    src={t.youtubeThumbnail}
                    alt={t.title}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Play button */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={() => setOpenId(t.youtubeUrl.split('=')[1])}
                    className="flex items-center justify-center bg-white/80 hover:bg-white/90 text-black rounded-full p-3 transition"
                    aria-label="Play video"
                  >
                    <Play className="h-8 w-8" />
                  </button>
                </div>

                {/* Social share (hover) */}
                <div className="absolute top-4 inset-x-0 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex space-x-2">
                    <button className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                      <Facebook className="h-4 w-4" />
                    </button>
                    <button className="p-2 bg-black text-white rounded-md hover:bg-gray-800 transition">
                      <X className="h-4 w-4" />
                    </button>
                    <button className="p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">
                      <Pin className="h-4 w-4" />
                    </button>
                    <button className="p-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 transition">
                      <MailIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Caption */}
                <div className="p-6 text-left">
                  <h3 className="text-lg font-bold uppercase">{t.title}</h3>
                  <p className="text-sm text-gray-700 mb-4">{t.destination}</p>
                  <div className="flex items-center text-gray-600 text-sm">
                    <Calendar className="h-5 w-5 mr-2" />
                    <span>
                      {new Date(t.date).toLocaleDateString('en-US', {
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric',
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Modal dialog */}
      {openId !== null && (
        <div
          className="fixed inset-0 bg-black/75 flex items-center justify-center z-50"
          onClick={() => setOpenId(null)}
        >
          <div
            className="relative w-full max-w-3xl mx-4 aspect-video bg-black"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setOpenId(null)}
              className="absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white z-10"
            >
              <X className="h-5 w-5" />
            </button>
            <iframe
              className="absolute inset-0 w-full h-full"
              src={`https://www.youtube.com/embed/${openId}?autoplay=1&rel=0`}
              title="Testimonial Video"
              allow="autoplay; encrypted-media; picture-in-picture"
              allowFullScreen
            />
          </div>
        </div>
      )}
    </>
  );
}
