import { useRouter } from 'next/navigation';
import React from 'react';

const TripPlanningBanner = () => {
    const router = useRouter();

    const handlePlanTrip = () => {
        router.push('/customize-my-trip');
    };

    return (
        <div className="w-full container mx-auto md:px-4 px-2">
            <div className="bg-secondary/20 rounded-xl p-6 md:p-8 flex flex-col md:flex-row justify-between items-center shadow-xl">
                <div className="text-primary mb-5 md:mb-0 text-center md:text-left">
                    <h2 className="text-xl md:text-2xl font-semibold mb-2 tracking-tight">
                        Not satisfied with this Itinerary?
                    </h2>
                    <p className="text-base md:text-lg opacity-95 font-normal">
                        Are you interested on planning custom trip? It only takes 2 minutes.
                    </p>
                </div>

                <button
                    onClick={handlePlanTrip}
                    className="bg-primary hover:bg-primary/80 
                     text-white font-semibold py-3 px-6 rounded-lg shadow-lg 
                     transition-all duration-300 ease-in-out transform hover:-translate-y-1 
                     hover:shadow-xl active:translate-y-0 whitespace-nowrap"
                >
                    Plan Your Trip
                </button>
            </div>
        </div>
    );
};

export default TripPlanningBanner;