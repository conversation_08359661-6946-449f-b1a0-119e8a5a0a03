import { Activity, Home, Utensils, Plane, Backpack, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import React from "react"

interface IncludedItem {
  id: string
  title: string
  desc: string
  icon: React.ElementType
}

const included: IncludedItem[] = [
  {
    id: "activities",
    title: "Activities & Certified Guides",
    desc: "All itinerary activities with local, expert, English‑speaking trekking & climbing guides plus a support crew.",
    icon: Activity,
  },
  {
    id: "accommodation",
    title: "All Accommodation",
    desc: "2 nights in a hotel, 1 night glamping, 13 nights in teahouses and 2 nights camping.",
    icon: Home,
  },
  {
    id: "meals",
    title: "Meals",
    desc: "4 breakfasts, 3 lunches and 4 dinners.",
    icon: Utensils,
  },
  {
    id: "flights",
    title: "Internal Flights & Transfers",
    desc: "Return internal flights Manthali ↔ Lukla; airport transfers on arrival & departure; all local transfers.",
    icon: Plane,
  },
  {
    id: "porterage",
    title: "Porterage, Permits & Equipment",
    desc: "Porters carry overnight luggage; trekking permits & fees handled; climbing equipment provided.",
    icon: Backpack,
  },
  {
    id: "groups",
    title: "Small Like‑Minded Groups",
    desc: "Solo‑friendly design; join sociable groups up to 10 like‑minded, active, outdoorsy people.",
    icon: Users,
  },
]

const WhatsIncludedSection = () => {
  return (
    <section className="relative bg-dark -mt-40 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row lg:items-start lg:gap-12">
          
            <div className="flex-1">
              <h2 className="text-2xl md:text-3xl font-extrabold tracking-tight mb-8">
                WHAT&apos;S INCLUDED?
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-4">
                {included.map(item => {
                  const Icon = item.icon
                  return (
                    <div key={item.id} className="flex items-start gap-4">
                      <div className="shrink-0 mt-1">
                        <span className="inline-flex h-10 w-10 items-center justify-center rounded-md bg-lime-600/10 text-brand ring-1 ring-lime-500/30">
                          <Icon className="h-6 w-6" />
                        </span>
                      </div>
                      <div>
                        <h3 className="text-sm font-bold uppercase tracking-wide text-white mb-1">
                          {item.title}
                        </h3>
                        <p className="text-xs leading-relaxed text-neutral-200">
                          {item.desc}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

          <div className="mt-12 lg:mt-0 lg:w-80 shrink-0">
            <div className="rounded-lg p-8 flex flex-col justify-between h-full">
              <div>
                <p className="text-xs uppercase tracking-wider text-neutral-400 mb-2">From</p>
                <div className="text-4xl md:text-5xl font-extrabold">
                  £2,510
                </div>
                <p className="text-xs mt-1 text-neutral-400">excluding flights</p>

              </div>

              <div className="mt-8">
                <Button className="w-full bg-brand hover:bg-brand/80 text-neutral-900 font-semibold">
                  Dates &amp; Prices
                </Button>
              </div>

              <p className="mt-4 text-[10px] leading-tight text-neutral-500">
                *Exact price may vary by season & group size. Secure your spot with a deposit.
              </p>
            </div>
          </div>

        </div>
      </div>
    </section>
  )
}

export default WhatsIncludedSection
