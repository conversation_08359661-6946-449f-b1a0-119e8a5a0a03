'use client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, Trophy } from 'lucide-react';
import { IHomeTailoredAdventure } from '@/types/home';

export function TailoredEveryAdventure({
  adventure,
}: {
  adventure: IHomeTailoredAdventure;
}) {
  return (
    <section className="py-8 md:py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {adventure.title}
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-12 md:gap-8">
          {adventure.features.map((feature) => (
            <div
              key={feature.id}
              className="text-center p-8 rounded-xl bg-gradient-to-b from-white to-brand/15 hover:shadow-xl transition-all duration-300 ease-in-out group cursor-pointer"
            >
              <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 border-2 border-gray-800 rounded-lg flex items-center justify-center">
                    <Trophy/>
                    {/* <Icon className="h-8 w-8 text-gray-800" /> */}
                  </div>
                </div>
              <h3 className="text-lg font-bold text-gray-900 mb-4 leading-tight">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                {feature.subtitle}
              </p>
              <Link href={feature.linkUrl}>
                <Button className=" text-white group">
                  {feature.linkLabel}
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
