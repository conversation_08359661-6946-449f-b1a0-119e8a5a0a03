import { Section } from '@/modules/fastpacking/components/detail'
import {
    Bus,
    Home,
    Calendar,
    ShoppingBag,
    ArrowRight,
    Dumbbell,
} from 'lucide-react'

export const tsumValleyTrek: Section[] = [
    {
        title: 'Free pickup and Trek Start',
        paragraphs: [
            'Your Tsum Valley Trek begins in Kathmandu. We recommend arriving at least a day early to get properly organized and rested.',
            'The trek officially starts with a drive to Machhakhola from Kathmandu or Pokhara, followed by hiking through remote Buddhist villages like Lokpa, Chhekampar, Nile, and Mu Gompa in the sacred Tsum Valley.',
        ],
        callouts: [
            {
                icon: Bus,
                text: 'Pickup from Kathmandu or Pokhara to Machhakhola and return drive is included in the package.',
            },
        ],
    },
    {
        title: 'Accommodations during the Trek',
        paragraphs: [
            'Accommodation is provided in basic tea houses or local lodges along the trail. Rooms usually have twin beds and shared bathrooms.',
            'Electricity is limited and often solar-powered. Charging and hot showers may require extra payment in some villages.',
        ],
        callouts: [
            {
                icon: Home,
                text: 'Accommodation in Kathmandu/Pokhara before and after the trek is not included in the package.',
            },
        ],
    },
    {
        title: 'Meals during Nar Phu Trek',
        paragraphs: [
            'You’ll receive three daily meals including local dishes such as dal bhat, Tibetan bread, and noodle soups.',
            'Tea and boiled water are provided. Some tea houses may also serve seasonal vegetables and light snacks.',
        ],
        callouts: [
            {
                icon: Calendar,
                text: 'Please notify us in advance of any dietary restrictions or allergies.',
            },
        ],
    },
    {
        title: 'Luggage Limit in Nar Phu Trek',
        paragraphs: [
            'Carry a small daypack with essentials like water, snacks, sunscreen, and rain gear. Your main bag will be carried by a porter.',
        ],
        callouts: [
            {
                icon: ShoppingBag,
                text: 'Duffel bag should not exceed 12–15 kg; daypack under 6 kg.',
            },
        ],
    },
    {
        title: 'Facilities & Essentials during Trek',
        bullets: [
            {
                icon: ArrowRight,
                title: 'Water',
                text: 'Boiled or filtered water will be provided daily. Carrying a personal purifier is recommended.',
            },
            {
                icon: ArrowRight,
                title: 'Connectivity',
                text: 'Most of the Tsum Valley has no mobile network or internet access. Expect limited or no signal.',
            },
        ],
    },
    {
        title: 'Travel Essentials',
        bullets: [
            {
                icon: ArrowRight,
                title: 'Visa',
                text: 'Tourist visas can be obtained on arrival at Tribhuvan International Airport for most nationalities.',
            },
            {
                icon: ArrowRight,
                title: 'Travel Insurance',
                text: 'Ensure your insurance covers trekking up to 3,700 m and includes emergency evacuation.',
            },
            {
                icon: ArrowRight,
                title: 'Currency',
                text: 'There are no ATMs in the region. Carry enough Nepali Rupees from Kathmandu or Pokhara.',
            },
            {
                icon: ArrowRight,
                title: 'Personal Expenses',
                text: 'Costs for snacks, soft drinks, hot showers, battery charging, and donations are not included.',
            },
        ],
    },
    {
        title: 'Best Time for Nar Phu Trek',
        paragraphs: [
            'Spring (March–May) and Autumn (September–November) are ideal with stable weather and excellent mountain views.',
            'Avoid monsoon season (June–August) due to heavy rain and slippery trails. Winter is cold and remote villages may be snow-covered.',
        ],
    },
    {
        title: 'Typical Day during the Trek',
        paragraphs: [
            'Each day starts with breakfast and a guide briefing. You’ll walk for 5–7 hours with breaks for lunch, rest, and exploration.',
            'Evenings are spent at tea houses with dinner, storytelling, and preparation for the next day’s trail.',
        ],
    },
    {
        title: 'How Difficult is the Nar Phu Trek?',
        paragraphs: [
            'Tsum Valley Trek is moderate to challenging. It involves long walking days, remote trails, and elevations up to 3,700 m.',
            'It’s suitable for physically fit trekkers with some hiking experience. Proper acclimatization is important.',
        ],
    },
    {
        title: 'What to Pack for the Trek?',
        paragraphs: [
            'Warm clothing, thermal base layers, a -10°C sleeping bag, gloves, headlamp, water bottles, trekking shoes, and rain jacket are essential.',
            'Also bring sunscreen, toiletries, personal medication, a quick-dry towel, and trekking poles (optional).',
        ],
    },
    {
        title: 'How to Prepare for the Trek?',
        paragraphs: [
            'Prepare by doing cardio, stair climbing, and leg workouts 3–4 weeks before the trek. Multi-day hikes help build endurance.',
            'Get used to carrying a loaded backpack and walking on uneven terrain. Mentally prepare for basic facilities and remote settings.',
        ],
        callouts: [
            {
                icon: Dumbbell,
                text: 'Train regularly to make your Tsum Valley Trek safer and more enjoyable.',
            },
        ],
    },
    {
        title: 'Nar Phu Trek without Guide',
        paragraphs: [
            'Unlike the Nar Phu region, Tsum Valley does not require a mandatory guide, but having one is highly recommended for safety and navigation.',
            'A guide ensures proper logistics, cultural insight, and helps in case of emergencies, especially in such remote areas.',
        ],
    },
];
