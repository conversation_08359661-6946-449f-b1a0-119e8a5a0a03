import React from 'react';
import { 
  Facebook, 
  Twitter, 
  Mail, 
  Pin, 
  Co<PERSON>, 
  Linkedin, 
  MessageCircle, 
  MessageSquare 
} from 'lucide-react';

interface SocialShareProps {
  url?: string;
  title?: string;
  description?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({ url, title, description }) => {
  const shareUrl = encodeURIComponent(url || window.location.href);
  const shareTitle = encodeURIComponent(title || document.title);
  const shareDescription = encodeURIComponent(description || '');

  const shareLinks = [
    {
      name: 'Facebook',
      url: `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`,
      icon: Facebook,
      bgColor: 'bg-blue-600 hover:bg-blue-700',
      textColor: 'text-white'
    },
    {
      name: 'X (Twitter)',
      url: `https://twitter.com/intent/tweet?url=${shareUrl}&text=${shareTitle}`,
      icon: Twitter,
      bgColor: 'bg-black hover:bg-gray-800',
      textColor: 'text-white'
    },
    {
      name: 'Em<PERSON>',
      url: `mailto:?subject=${shareTitle}&body=${shareDescription}%20${shareUrl}`,
      icon: Mail,
      bgColor: 'bg-gray-600 hover:bg-gray-700',
      textColor: 'text-white'
    },
    {
      name: 'Pinterest',
      url: `https://pinterest.com/pin/create/button/?url=${shareUrl}&description=${shareTitle}`,
      icon: Pin,
      bgColor: 'bg-red-600 hover:bg-red-700',
      textColor: 'text-white'
    },
    {
      name: 'Copy Link',
      url: '#',
      icon: Copy,
      bgColor: 'bg-green-600 hover:bg-green-700',
      textColor: 'text-white',
      onClick: () => {
        navigator.clipboard.writeText(url || window.location.href);
        alert('Link copied to clipboard!');
      }
    },
    {
      name: 'LinkedIn',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}`,
      icon: Linkedin,
      bgColor: 'bg-blue-500 hover:bg-blue-600',
      textColor: 'text-white'
    },
    {
      name: 'WhatsApp',
      url: `https://wa.me/?text=${shareTitle}%20${shareUrl}`,
      icon: MessageCircle,
      bgColor: 'bg-green-500 hover:bg-green-600',
      textColor: 'text-white'
    },
    {
      name: 'Messenger',
      url: `https://www.facebook.com/dialog/send?link=${shareUrl}`,
      icon: MessageSquare,
      bgColor: 'bg-blue-400 hover:bg-blue-500',
      textColor: 'text-white'
    }
  ];

  const handleShare = (link: {
    name: string;
    url: string;
    icon: React.ComponentType<{ size?: number; className?: string }>;
    bgColor: string;
    textColor: string;
    onClick?: () => void;
  }) => {
    if (link.onClick) {
      link.onClick();
    } else {
      window.open(link.url, '_blank', 'width=600,height=400');
    }
  };

  return (
    <div className="py-6 px-4">
      <h3 className="text-lg font-medium text-gray-800 text-center mb-4">
        Share with your Friends
      </h3>
      <div className="flex flex-wrap justify-center gap-3">
        {shareLinks.map((link, index) => {
          const IconComponent = link.icon;
          return (
            <button
              key={index}
              onClick={() => handleShare(link)}
              className={`w-8 h-8 rounded-sm ${link.bgColor} ${link.textColor} flex items-center justify-center transition-colors duration-200`}
              title={link.name}
              aria-label={`Share on ${link.name}`}
            >
              <IconComponent size={16} />
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SocialShare;