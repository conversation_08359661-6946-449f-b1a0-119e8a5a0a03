'use client';

import type React from 'react';
import { Mountain, Activity, Gauge, SunMoon } from 'lucide-react';
import { ActivityCategoryCard } from './card/activity-card';
import { IHomeAdventure } from '@/types/home';

interface Stat {
  icon: React.ReactNode;
  label: string;
  value: string;
}

interface Category {
  slug: string;
  title: string;
  images: string[];
  stats: Stat[];
}

const categories: Category[] = [
  {
    slug: 'trail-running',
    title: 'Trail Running',
    images: [
      '/images/fastpacking/hero/image3.webp',
      '/images/fastpacking/hero/image1.webp',
      '/images/fastpacking/hero/image1.webp',
      '/images/fastpacking/hero/image4.webp',
      '/images/fastpacking/hero/image5.webp',
      '/images/fastpacking/hero/image6.webp',
      '/images/fastpacking/hero/image7.webp',
    ],

    stats: [
      {
        icon: <Mountain className="h-5 w-5" />,
        label: 'Peaks',
        value: '20+ Exclusive',
      },
      {
        icon: <Gauge className="h-5 w-5" />,
        label: 'Grade',
        value: 'Moderate to Technical',
      },
      {
        icon: <Activity className="h-5 w-5" />,
        label: 'Activity',
        value: '4–8 Hrs',
      },
      {
        icon: <SunMoon className="h-5 w-5" />,
        label: 'Seasons',
        value: 'Spring / Autumn',
      },
    ],
  },
  {
    slug: 'peak-climbing',
    title: 'Peak Climbing',
    images: [
      '/images/fastpacking/hero/image2.webp',
      '/images/fastpacking/hero/image1.webp',
      '/images/fastpacking/hero/image3.webp',
      '/images/fastpacking/hero/image4.webp',
      '/images/fastpacking/hero/image5.webp',
      '/images/fastpacking/hero/image6.webp',
      '/images/fastpacking/hero/image7.webp',
    ],
    stats: [
      {
        icon: <Mountain className="h-5 w-5" />,
        label: 'Peaks',
        value: '20+ Exclusive',
      },
      {
        icon: <Gauge className="h-5 w-5" />,
        label: 'Grade',
        value: 'Moderate to Technical',
      },
      {
        icon: <Activity className="h-5 w-5" />,
        label: 'Activity',
        value: '4–8 Hrs',
      },
      {
        icon: <SunMoon className="h-5 w-5" />,
        label: 'Seasons',
        value: 'Spring / Autumn',
      },
    ],
  },
  {
    slug: 'trekking',
    title: 'Trekking',
    images: [
      '/images/fastpacking/hero/image1.webp',
      '/images/fastpacking/hero/image2.webp',
      '/images/fastpacking/hero/image3.webp',
      '/images/fastpacking/hero/image4.webp',
      '/images/fastpacking/hero/image5.webp',
      '/images/fastpacking/hero/image6.webp',
      '/images/fastpacking/hero/image7.webp',
    ],
    stats: [
      {
        icon: <Mountain className="h-5 w-5" />,
        label: 'Peaks',
        value: '20+ Exclusive',
      },
      {
        icon: <Gauge className="h-5 w-5" />,
        label: 'Grade',
        value: 'Moderate to Technical',
      },
      {
        icon: <Activity className="h-5 w-5" />,
        label: 'Activity',
        value: '4–8 Hrs',
      },
      {
        icon: <SunMoon className="h-5 w-5" />,
        label: 'Seasons',
        value: 'Spring / Autumn',
      },
    ],
  },
];

export default function ActivityCategories({
  adventure,
}: {
  adventure: IHomeAdventure;
}) {
  return (
    <div className='bg-brand/5' >
      <section className="relative container mx-auto px-4 py-8 md:py-16">
        <div className="mb-10 text-center">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
            {adventure.title}
          </h2>
        </div>

        <div
          className="grid gap-6 md:grid-cols-3 md:auto-rows-[420px] overflow-x-auto md:overflow-visible snap-x md:snap-none pb-2 -mx-4 px-4 md:mx-0 md:px-0"
          role="list"
          aria-label="Activity categories"
        >
          {categories.map((cat, i) => (
            <ActivityCategoryCard
              key={cat.slug}
              index={i}
              slug={cat.slug}
              title={cat.title}
              images={cat.images}
              stats={cat.stats}
            />
          ))}
        </div>
      </section>
    </div>
  );
}
