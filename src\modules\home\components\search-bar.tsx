import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

const SearchBar = () => {
    const router = useRouter()
    const [q, setQ] = useState('')

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        const query = q.trim()
        if (!query) return
        router.push(`/search?query=${encodeURIComponent(query)}`)
    }

    return (
        <div>
            <div className="max-w-2xl mx-auto">
                <form onSubmit={onSubmit} role="search" aria-label="Part search">
                    <div className="relative">
                        <Input
                            type="text"
                            value={q}
                            onChange={(e) => setQ(e.target.value)}
                            placeholder="Search Trips by Name"
                            className="w-full h-14 pl-6 pr-14 text-lg bg-light/95 backdrop-blur-sm border-0 rounded-full shadow-lg placeholder:text-gray-500 focus:ring-2 focus:ring-brand focus:ring-offset-0"
                        />
                        <Button
                            type="submit"
                            size="icon"
                            className="absolute right-2 top-2 h-10 w-10 rounded-full bg-brand hover:bg-brand/80 text-light z-10"
                        >
                            <Search className="h-5 w-5" />
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    )
}

export default SearchBar