'use client'

import Image from 'next/image'
import React from 'react'

interface ImageData {
    src: string
    alt: string
    priority?: boolean
}

interface HeroImageGridProps {
    images: ImageData[]
    title: string
    subtitle?: string
}

const HeroImageGrid: React.FC<HeroImageGridProps> = ({
    images,
    title,
    subtitle,
}) => {
    const gridImages = images.slice(0, 8)

    return (
        <div className="relative md:h-screen h-[450px] overflow-hidden">
            <div className="absolute inset-0 grid grid-cols-3 gap-1">
                <div className="grid grid-rows-2 gap-1">
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[0]?.src || "/images/footer-image.png"}
                            alt={gridImages[0]?.alt || "Image 1"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                            priority={gridImages[0]?.priority}
                        />
                    </div>
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[1]?.src || "/images/footer-image.png"}
                            alt={gridImages[1]?.alt || "Image 2"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                </div>

                <div className="grid grid-rows-3 gap-1">
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[3]?.src || "/images/footer-image.png"}
                            alt={gridImages[3]?.alt || "Image 4"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[4]?.src || "/images/footer-image.png"}
                            alt={gridImages[4]?.alt || "Image 5"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[2]?.src || "/images/footer-image.png"}
                            alt={gridImages[2]?.alt || "Image 3"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                </div>

                <div className="grid grid-rows-2 gap-1">
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[5]?.src || "/images/footer-image.png"}
                            alt={gridImages[5]?.alt || "Image 6"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                    <div className="relative overflow-hidden">
                        <Image
                            src={gridImages[6]?.src || "/images/footer-image.png"}
                            alt={gridImages[6]?.alt || "Image 7"}
                            fill
                            className="object-cover transition-transform duration-700 hover:scale-105"
                        />
                    </div>
                </div>

            </div>

            <div className="absolute inset-0 bg-black/4"></div>

            <div className="relative z-10 flex h-full items-end justify-start pb-16 md:pb-24">
                <div className="container mx-auto px-4 text-left">
                    <h1 className="text-5xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg">
                        {title}
                    </h1>
                    {subtitle && (
                        <p className="text-xl md:text-2xl text-white/90 drop-shadow-md">
                            {subtitle}
                        </p>
                    )}
                </div>
            </div>
        </div>
    )
}

export default HeroImageGrid