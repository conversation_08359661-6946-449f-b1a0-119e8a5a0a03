// components/StackedReviews.tsx
'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Star, Voicemail } from 'lucide-react'
import Image from 'next/image'
import { Card } from '@/components/ui/card'

export interface Review {
  id: string | number
  name: string
  role: string
  company: string
  rating: number          
  content: string
  avatar: string         
}

export interface StackedReviewsProps {
  reviews: Review[]
  headerTitle?: string
  headerIcon?: React.ElementType
  containerHeightVH?: number
  stickyTopClass?: string
}

const StackedReviews: React.FC<StackedReviewsProps> = ({
  reviews,
  headerTitle = "Travelers' Review",
  headerIcon: HeaderIcon = Voicemail,
  containerHeightVH = 70,
  stickyTopClass = 'top-40',
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const handle = () => {
      if (!containerRef.current) return
      const rect = containerRef.current.getBoundingClientRect()
      const cHeight = containerRef.current.offsetHeight
      const wHeight = window.innerHeight

      const scrollStart = -rect.top
      const scrollEnd = cHeight - wHeight
      setProgress(Math.max(0, Math.min(1, scrollStart / scrollEnd)))
    }

    window.addEventListener('scroll', handle, { passive: true })
    handle()
    return () => window.removeEventListener('scroll', handle)
  }, [])

  return (
    <section className="container mx-auto px-4 py-12">
      <div
        ref={containerRef}
        className="relative"
        style={{ height: `${reviews.length * containerHeightVH}vh` }}
      >
        <div className={`sticky ${stickyTopClass} h-[70vh] md:h-90 flex flex-col items-start`}>
          {/* Header */}
          <div className="mb-2">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                <HeaderIcon size={16} className="text-light" />
              </div>
              <h2 className="text-2xl md:text-3xl font-bold text-brand">
                {headerTitle}
              </h2>
            </div>
          </div>

          <div className="relative w-full mx-auto h-full overflow-visible">
            {reviews.map((r, i) => {
              const start = i / reviews.length
              const end = (i + 1) / reviews.length
              const cardProg =
                i === 0
                  ? 1
                  : Math.max(0, Math.min(1, (progress - start) / (end - start)))

              const baseY = 400
              const finalY = i * 12
              const y = baseY - cardProg * (baseY - finalY)
              const scale = 0.8 + cardProg * 0.2
              const opacity = cardProg
              const zIndex = i + 1

              return (
                <Card
                  key={r.id}
                  className="absolute inset-0 p-6 bg-green-50 border-0 shadow-2xl"
                  style={{
                    transform: `translateY(${y}px) scale(${scale})`,
                    opacity,
                    zIndex,
                  }}
                >
                  <div className="h-full flex flex-col justify-between space-y-4">
                    <div>
                      <div className="flex items-center space-x-1 mb-3">
                        {[...Array(5)].map((_, starIdx) => (
                          <Star
                            key={starIdx}
                            className={`w-4 h-4 ${
                              starIdx < r.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <blockquote className="text-gray-700 text-base leading-relaxed">
                        “{r.content}”
                      </blockquote>
                    </div>
                    <div className="flex items-center space-x-3 pt-4 border-t border-gray-100">
                      <Image
                        src={r.avatar}
                        alt={r.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div>
                        <div className="font-semibold text-gray-900">{r.name}</div>
                        <div className="text-sm text-gray-600">
                          {r.role} at {r.company}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}

export default StackedReviews


// "use client"

// import { useEffect, useRef, useState } from "react"
// import { Star, Voicemail } from "lucide-react"
// import { Card } from "@/components/ui/card"
// import Image from "next/image"

// const reviews = [
//     {
//         id: 1,
//         name: "Sarah Johnson",
//         role: "Product Manager",
//         company: "TechCorp",
//         rating: 5,
//         content:
//             "This product has completely transformed how our team collaborates. The interface is intuitive and the features are exactly what we needed.",
//         avatar: "/placeholder.svg?height=60&width=60",
//     },
//     {
//         id: 2,
//         name: "Michael Chen",
//         role: "Software Engineer",
//         company: "StartupXYZ",
//         rating: 5,
//         content:
//             "Outstanding performance and reliability. We've been using this for 6 months now and it's been rock solid. Highly recommend!",
//         avatar: "/placeholder.svg?height=60&width=60",
//     },
//     {
//         id: 3,
//         name: "Emily Rodriguez",
//         role: "Design Lead",
//         company: "Creative Studio",
//         rating: 4,
//         content:
//             "The design is beautiful and user-friendly. It's made our workflow so much more efficient. The customer support is also top-notch.",
//         avatar: "/placeholder.svg?height=60&width=60",
//     },
//     {
//         id: 4,
//         name: "David Thompson",
//         role: "Marketing Director",
//         company: "Growth Co",
//         rating: 5,
//         content:
//             "Incredible value for money. The analytics features have given us insights we never had before. Game changer for our business.",
//         avatar: "/placeholder.svg?height=60&width=60",
//     },
//     {
//         id: 5,
//         name: "Lisa Wang",
//         role: "CEO",
//         company: "Innovation Labs",
//         rating: 5,
//         content:
//             "This solution has scaled perfectly with our growing team. The integration capabilities are fantastic and save us hours every week.",
//         avatar: "/placeholder.svg?height=60&width=60",
//     },
// ]

// export function StackedReviews() {
//     const containerRef = useRef<HTMLDivElement>(null)
//     const [scrollProgress, setScrollProgress] = useState(0)

//     useEffect(() => {
//         const handleScroll = () => {
//             if (!containerRef.current) return

//             const rect = containerRef.current.getBoundingClientRect()
//             const containerHeight = containerRef.current.offsetHeight
//             const windowHeight = window.innerHeight

//             const scrollStart = -rect.top
//             const scrollEnd = containerHeight - windowHeight
//             const progress = Math.max(0, Math.min(1, scrollStart / scrollEnd))

//             setScrollProgress(progress)
//         }

//         window.addEventListener("scroll", handleScroll, { passive: true })
//         handleScroll()
//         return () => window.removeEventListener("scroll", handleScroll)
//     }, [])

//     return (
//         <section className="container mx-auto px-4 py-12">
//             <div
//                 ref={containerRef}
//                 className="relative"
//                 style={{ height: `${reviews.length * 70}vh` }}
//             >
//                 <div className="sticky top-40 h-90 flex flex-col items-start">
//                     <div className="mb-2">
//                         <div className="flex items-center gap-3 mb-6">
//                             <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//                                 <Voicemail size={16} className="text-light" />
//                             </div>
//                             <h2 className="text-2xl md:text-3xl font-bold text-brand">Travelers&apos; Review</h2>
//                         </div>
//                     </div>
//                     <div className="relative w-full mx-auto h-full overflow-visible">
//                         {reviews.map((review, index) => {
//                             const cardStart = index === 1
//                                 ? 0.1         
//                                 : index / reviews.length
//                             const cardEnd = (index + 1) / reviews.length
//                             const cardProgress =
//                                 index === 0
//                                     ? 1
//                                     : Math.max(
//                                         0,
//                                         Math.min(1, (scrollProgress - cardStart) / (cardEnd - cardStart))
//                                     )

//                             const baseTranslateY = 400
//                             const finalTranslateY = index * 12
//                             const currentTranslateY =
//                                 baseTranslateY -
//                                 cardProgress * (baseTranslateY - finalTranslateY)

//                             const scale = 0.8 + cardProgress * 0.2
//                             const opacity = cardProgress
//                             const zIndex = index + 1

//                             return (
//                                 <Card
//                                     key={review.id}
//                                     className="absolute inset-0 p-6 bg-green-50 border-brand shadow-2xl border-0"
//                                     style={{
//                                         transform: `translateY(${currentTranslateY}px) scale(${scale})`,
//                                         opacity,
//                                         zIndex,
//                                     }}
//                                 >
//                                     <div className="h-full flex flex-col justify-between space-y-4">
//                                         <div>
//                                             <div className="flex items-center space-x-1 mb-3">
//                                                 {[...Array(5)].map((_, i) => (
//                                                     <Star
//                                                         key={i}
//                                                         className={`w-4 h-4 ${i < review.rating
//                                                             ? "text-yellow-400 fill-current"
//                                                             : "text-gray-300"
//                                                             }`}
//                                                     />
//                                                 ))}
//                                             </div>
//                                             <blockquote className="text-gray-700 text-base leading-relaxed">
//                                                 “{review.content}”
//                                             </blockquote>
//                                         </div>
//                                         <div className="flex items-center space-x-3 pt-4 border-t border-gray-100">
//                                             <Image
//                                                 src={review.avatar}
//                                                 alt={review.name}
//                                                 width={48}
//                                                 height={48}
//                                                 className="w-12 h-12 rounded-full object-cover"
//                                             />
//                                             <div>
//                                                 <div className="font-semibold text-gray-900">
//                                                     {review.name}
//                                                 </div>
//                                                 <div className="text-sm text-gray-600">
//                                                     {review.role} at {review.company}
//                                                 </div>
//                                             </div>
//                                         </div>
//                                     </div>
//                                 </Card>
//                             )
//                         })}
//                     </div>
//                 </div>
//             </div>
//         </section>
//     )
// }
