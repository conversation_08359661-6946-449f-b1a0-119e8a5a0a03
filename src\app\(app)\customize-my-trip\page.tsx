'use client'

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

interface FormData {
    tripType: string;
    numberOfPersons: number;
    duration: number;
    arrivalDate: string;
    questions: string;
    fullName: string;
    email: string;
    country: string;
    contactNumber: string;
    agreeToTerms: boolean;
}

type FormFieldKey = keyof FormData;
type FormFieldValue = FormData[FormFieldKey];

const PlanYourTripForm: React.FC = () => {
    const [formData, setFormData] = useState<FormData>({
        tripType: '',
        numberOfPersons: 1,
        duration: 1,
        arrivalDate: '',
        questions: '',
        fullName: '',
        email: '',
        country: '',
        contactNumber: '',
        agreeToTerms: false
    });

    const handleInputChange = (field: FormFieldKey, value: FormFieldValue): void => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const incrementValue = (field: 'numberOfPersons' | 'duration'): void => {
        setFormData(prev => ({
            ...prev,
            [field]: prev[field] + 1
        }));
    };

    const decrementValue = (field: 'numberOfPersons' | 'duration'): void => {
        setFormData(prev => ({
            ...prev,
            [field]: Math.max(1, prev[field] - 1)
        }));
    };

    const handleSubmit = () => {
        console.log('Form submitted:', formData);
        // Handle form submission here
    };

    return (
        <div
            className="py-16 px-4 relative bg-cover bg-center bg-no-repeat"
            style={{
                backgroundImage: "url('/images/footer-image.png')"
            }}
        >
            <div className="absolute inset-0 bg-light/80"></div>
            <div className='relative z-10'>
                <div className="container mx-auto px-4 py-8">
                    <div className="text-center mb-8">
                        <h1 className="text-4xl font-bold text-brand">Plan your Trip</h1>
                        <p className="text-lg text-dark/80">Plan and book your dream adventure with us</p>
                    </div>
                </div>
                <div className="max-w-2xl mx-auto bg-white shadow-lg overflow-hidden">
                    <div className="bg-brand text-white">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold mb-4 bg-brand px-4 py-2 rounded">
                                Fill The Details
                            </h3>

                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">Type Of Trip</label>
                                <select
                                    value={formData.tripType}
                                    onChange={(e) => handleInputChange('tripType', e.target.value)}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Type</option>
                                    <option value="private">Private</option>
                                    <option value="group">Group</option>
                                </select>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium mb-2">Number Of Person*</label>
                                    <div className="flex items-center gap-2">
                                        <button
                                            onClick={() => decrementValue('numberOfPersons')}
                                            className="w-8 h-8 bg-white text-gray-900 rounded flex items-center justify-center hover:bg-gray-100"
                                        >
                                            <Minus size={16} />
                                        </button>
                                        <input
                                            type="number"
                                            value={formData.numberOfPersons}
                                            onChange={(e) => handleInputChange('numberOfPersons', parseInt(e.target.value) || 1)}
                                            className="flex-1 px-3 py-2 bg-white text-gray-900 rounded border text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            min="1"
                                        />
                                        <button
                                            onClick={() => incrementValue('numberOfPersons')}
                                            className="w-8 h-8 bg-white text-gray-900 rounded flex items-center justify-center hover:bg-gray-100"
                                        >
                                            <Plus size={16} />
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-2">Duration Of Trek/Travel*</label>
                                    <div className="flex items-center gap-2">
                                        <button
                                            onClick={() => decrementValue('duration')}
                                            className="w-8 h-8 bg-white text-gray-900 rounded flex items-center justify-center hover:bg-gray-100"
                                        >
                                            <Minus size={16} />
                                        </button>
                                        <input
                                            type="number"
                                            value={formData.duration}
                                            onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || 1)}
                                            className="flex-1 px-3 py-2 bg-white text-gray-900 rounded border text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            min="1"
                                        />
                                        <button
                                            onClick={() => incrementValue('duration')}
                                            className="w-8 h-8 bg-white text-gray-900 rounded flex items-center justify-center hover:bg-gray-100"
                                        >
                                            <Plus size={16} />
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">Planned Arrival Date*</label>
                                <div className="relative">
                                    <input
                                        type="date"
                                        value={formData.arrivalDate}
                                        onChange={(e) => handleInputChange('arrivalDate', e.target.value)}
                                        className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            </div>

                            <div className="mb-6">
                                <label className="block text-sm font-medium mb-2">Any Questions? Ask Us!</label>
                                <textarea
                                    value={formData.questions}
                                    onChange={(e) => handleInputChange('questions', e.target.value)}
                                    rows={4}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                    placeholder="Please provide any other relevant information or specific requests for your customized trip"
                                />
                            </div>
                        </div>

                        <div className="px-6 pb-6">
                            <h3 className="text-lg font-semibold mb-4 bg-slate-700 px-4 py-2 rounded">
                                Your Personal Details (Trip Leader)
                            </h3>

                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">Full Name*</label>
                                <input
                                    type="text"
                                    value={formData.fullName}
                                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter your full name"
                                />
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">Email Address*</label>
                                <input
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => handleInputChange('email', e.target.value)}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter your email"
                                />
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium mb-2">Country*</label>
                                <select
                                    value={formData.country}
                                    onChange={(e) => handleInputChange('country', e.target.value)}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="UK">United Kingdom</option>
                                    <option value="CA">Canada</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="JP">Japan</option>
                                    <option value="IN">India</option>
                                    <option value="NP">Nepal</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div className="mb-6">
                                <label className="block text-sm font-medium mb-2">Contact Number*</label>
                                <input
                                    type="tel"
                                    value={formData.contactNumber}
                                    onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                                    className="w-full px-3 py-2 bg-white text-gray-900 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter your contact number"
                                />
                            </div>

                            <div className="mb-6">
                                <label className="flex items-start gap-3 text-sm">
                                    <input
                                        type="checkbox"
                                        checked={formData.agreeToTerms}
                                        onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                                        className="mt-1 w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <span className="text-xs leading-tight">
                                        By submitting this form, I acknowledge that the information provided is true and accurate to the best of my knowledge. I understand that this expedition company will use this information to create a customized trip proposal based on my preferences and requirements.
                                    </span>
                                </label>
                            </div>

                            {/* <div className="mb-6">
                        <div className="bg-gray-200 border border-gray-300 rounded p-4 text-center text-gray-600">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <input type="checkbox" className="w-4 h-4" />
                                <span className="text-sm text-gray-800">I&apos;m not a robot</span>
                            </div>
                            <div className="text-xs text-gray-500">reCAPTCHA</div>
                        </div>
                    </div> */}

                            <button
                                onClick={handleSubmit}
                                className="w-full bg-dark hover:bg-dark/80 text-white font-semibold py-3 px-6 rounded transition-colors duration-200"
                            >
                                Send Your Enquiry
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PlanYourTripForm;