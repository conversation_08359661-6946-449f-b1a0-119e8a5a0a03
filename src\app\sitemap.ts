import getPackageSeo from "@/actions/package/get-package-seo";
import getPackages from "@/actions/package/get-packages";
import getHomeSeo from "@/actions/home/<USER>";
import getActivities from "@/actions/activity/get-activities";
import type { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://trailandtreknepal.com";

  const homeSeoData = await getHomeSeo();
  const homePriority = homeSeoData?.data?.sitemapPriority && homeSeoData.data.sitemapPriority > 0
    ? homeSeoData.data.sitemapPriority
    : 1;
  const homeChangeFreq = homeSeoData?.data?.changefreq || "weekly";

  const packagesData = await getPackages();
  const publishedPackages = packagesData.data.filter(pkg => pkg.published);

  const seoDataArray = await Promise.all(
    publishedPackages.map(pkg => getPackageSeo(pkg.slug))
  );

  const packageUrlsMap = new Map(publishedPackages.map((pkg, i) => {
    const seoData = seoDataArray[i];
    const priorityValue = seoData?.data?.sitemapPriority;
    const priority = priorityValue && priorityValue > 0 ? priorityValue : 0.7;
    const changefreq = seoData?.data?.changefreq || "monthly";

    return [pkg.slug, {
      urlSlug: pkg.slug,
      priority,
      changefreq,
      lastModified: pkg.updatedAt ? new Date(pkg.updatedAt) : new Date(),
    }];
  }));

  const activitiesData = await getActivities();
  const activities = activitiesData.data;

    const activityUrls = activities.map(activity => ({
    url: `${baseUrl}/${activity.slug}`,
    lastModified: activity.updatedAt ? new Date(activity.updatedAt) : new Date(),
    changeFrequency: "monthly",
    priority: 0.6,
  }));

  
  const activityPackageUrls = activities.flatMap(activity => {
    const validPackages = (activity.packages || []).filter(pkg => packageUrlsMap.has(pkg.slug));

    return validPackages.map(pkg => {
      const packageMeta = packageUrlsMap.get(pkg.slug)!;
      return {
        url: `${baseUrl}/${activity.slug}/${pkg.slug}`,
        lastModified: packageMeta.lastModified,
        changeFrequency: packageMeta.changefreq,
        priority: packageMeta.priority,
      };
    });
  });

  const staticUrls: MetadataRoute.Sitemap = [
    {
      url: `${baseUrl}/`,
      lastModified: new Date(),
      changeFrequency: homeChangeFreq,
      priority: homePriority,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: `${baseUrl}/fastpacking-gears`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: `${baseUrl}/teams`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: `${baseUrl}/blogs`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
  ];

  return [...staticUrls, ...activityUrls, ...activityPackageUrls];
}
