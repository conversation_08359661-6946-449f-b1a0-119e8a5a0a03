import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Clock, Navigation, Car } from 'lucide-react';
import React from 'react';

const ItineraryDhorpatan = () => {
  return (
    <div className="mt-12">
      <div className="relative mb-2">
        <h2 className="text-2xl md:text-4xl font-bold text-gray-800 py-8 relative">
          Dhorpatan Fastpacking Itinerary
        </h2>
        <div className="absolute bottom-6 w-15 rounded-full h-1 bg-brand"></div>
      </div>

      <Accordion type="multiple" className="space-y-4">
        <AccordionItem value="day-1">
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <div className="flex items-center gap-4 w-full">
              <div className="flex flex-col items-center">
                <span className="text-sm text-brand font-medium">Day</span>
                <span className="text-2xl font-bold text-brand">1</span>
              </div>
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-gray-900">
                  Pokhara to BhurtiBang to Dhorpatan
                </h3>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="flex items-center gap-2">
                  <Car size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Driving: <strong>6-7 hrs + 3 hrs</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <Navigation size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Distance: <strong>160 km + 25 km</strong></span>
                </div>
              </div>
              <div className="bg-brand/5 border border-brand rounded-lg p-4">
                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                  <div>Pokhara: 820 m</div>
                  <div>Bhurtibang: 1,337 m</div>
                  <div>Dhorpatan: 2,850 / 3,900 m</div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="day-2">
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <div className="flex items-center gap-4 w-full">
              <div className="flex flex-col items-center">
                <span className="text-sm text-brand font-medium">Day</span>
                <span className="text-2xl font-bold text-brand">2</span>
              </div>
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-gray-900">
                  Dhorpatan to Gurjaghat and Jhaljala (loop back)
                </h3>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="flex items-center gap-2">
                  <Navigation size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Distance: <strong>26 km</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Time: <strong>6-7 hours</strong></span>
                </div>
              </div>
              <div className="bg-brand/5 border border-brand rounded-lg p-4">
                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                  <div>Dhorpatan: 2,850 / 3,900 m</div>
                  <div>Gurjaghat: 3,015 m</div>
                  <div>Jhaljala: 3,390 m</div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="day-3">
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <div className="flex items-center gap-4 w-full">
              <div className="flex flex-col items-center">
                <span className="text-sm text-brand font-medium">Day</span>
                <span className="text-2xl font-bold text-brand">3</span>
              </div>
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-gray-900">
                  Gurjaghat to Gurjakhani
                </h3>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="flex items-center gap-2">
                  <Navigation size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Distance: <strong>14 km</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Time: <strong>4 hours</strong></span>
                </div>
              </div>
              <div className="bg-brand/5 border border-brand rounded-lg p-4">
                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                  <div>Gurjaghat: 3,015 m</div>
                  <div>Gurjakhani: 2,650 m</div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="day-4">
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <div className="flex items-center gap-4 w-full">
              <div className="flex flex-col items-center">
                <span className="text-sm text-brand font-medium">Day</span>
                <span className="text-2xl font-bold text-brand">4</span>
              </div>
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-gray-900">
                  Gurjakhani to Muna
                </h3>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="flex items-center gap-2">
                  <Navigation size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Distance: <strong>15 km</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Time: <strong>6 hours</strong></span>
                </div>
              </div>
              <div className="bg-brand/5 border border-brand rounded-lg p-4">
                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                  <div>Gurjakhani: 2,650 m</div>
                  <div>Muna: 2,720 m</div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="day-5">
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <div className="flex items-center gap-4 w-full">
              <div className="flex flex-col items-center">
                <span className="text-sm text-brand font-medium">Day</span>
                <span className="text-2xl font-bold text-brand">5</span>
              </div>
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-gray-900">
                  Drive from Muna to Beni to Pokhara
                </h3>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="flex items-center gap-2">
                  <Car size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Drive Time: <strong>3-4 hrs + 3-4 hrs</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <Navigation size={16} className="text-dark/80" />
                  <span className="text-sm text-dark/90">Distance: <strong>65 km + 98 km</strong></span>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default ItineraryDhorpatan;