'use client'

import { MountainSnow } from 'lucide-react';
import React from 'react';

interface PricingTier {
  persons: string;
  price: string;
}

interface PricingCardProps {
  duration?: string;
  originalPrice?: string;
  currentPrice?: string;
  pricingTiers?: PricingTier[];
  onInquireNow?: () => void;
  onDatesPrice?: () => void;
  onBookNow?: () => void;
}

const PricingCard: React.FC<PricingCardProps> = ({
  duration = "11 Nights And 12 Days",
  originalPrice = "USD 2500",
  currentPrice = "USD 1999 pp",
  pricingTiers = [
    { persons: "1 Pax", price: "$ 1999" },
    { persons: "2 Paxes", price: "$ 1950" },
    { persons: "3 - 6 Paxes", price: "$ 1900" },
    { persons: "7 - 12 Paxes", price: "$ 1800" },
    { persons: "13 - 25 Paxes", price: "$ 1650" }
  ],
  onInquireNow,
  onDatesPrice,
  onBookNow
}) => {

  const handleInquireNow = () => {
    if (onInquireNow) {
      onInquireNow();
    } else {
      console.log('Inquire Now clicked');
    }
  };

  const handleDatesPrice = () => {
    if (onDatesPrice) {
      onDatesPrice();
    } else {
      console.log('Dates & Price clicked');
    }
  };

  const handleBookNow = () => {
    if (onBookNow) {
      onBookNow();
    } else {
      console.log('Book Now clicked');
    }
  };

  return (
    <div className="bg-light border border-dark/80 rounded-sm overflow-hidden shadow-sm">
      <div className="relative flex justify-center">
        <div className="bg-red text-light inline-flex items-center space-x-2 py-2 px-4">
          <MountainSnow size={16} className="text-light" />
          <span className="font-semibold text-sm">PRIVATE TRIP</span>
        </div>
      </div>

      <div className=" space-y-1">
        <div className="px-4 text-center">
          <p className="text-dark/80 text-sm">{duration}</p>
          <div className="">
            <span className="text-dark/80 line-through text-sm">Price Start from {originalPrice}</span>
          </div>
          <div className="text-2xl font-bold text-dark/90 mt-1 ">
            {currentPrice}
          </div>
        </div>

        <div className='bg-brand/10'>
        <div className='px-4'>
          <div className="text-center">
            <p className="text-brand font-medium mt-2 text-sm">We Offer Group Discount</p>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between items-center text-sm font-medium text-dark/80 border-b pb-1">
              <span>No. of Persons</span>
              <span>Price per Person</span>
            </div>
            {pricingTiers.map((tier, index) => (
              <div key={index} className="flex justify-between items-center text-sm py-1 border-b border-gray-100">
                <span className="text-gray-600">{tier.persons}</span>
                <span className="text-gray-900 font-medium">{tier.price}</span>
              </div>
            ))}
          </div>
          </div>
        </div>

        <div className=" p-4 space-y-3 pt-2">
          <button
            onClick={handleInquireNow}
            className="w-full bg-red hover:bg-red/80 text-light font-medium py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Inquire Now
          </button>

          <div className="flex space-x-2">
            <button
              onClick={handleDatesPrice}
              className="flex-1 border border-gray-300 text-gray-700 hover:bg-gray-50 font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Dates & Price
            </button>
            <button
              onClick={handleBookNow}
              className="flex-1 bg-brand hover:bg-brand/80 text-light font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingCard;