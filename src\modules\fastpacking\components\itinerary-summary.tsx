'use client';

import React from 'react';
import { List } from 'lucide-react';

export interface DaySummary {
  day: number;
  title: string;
}

interface ItinerarySummaryProps {
  title: string;
  days: DaySummary[];
}

const ItinerarySummary: React.FC<ItinerarySummaryProps> = ({ title, days }) => {
  return (
    <section className="mt-12">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
          <List size={16} className="text-light" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-brand">{title}</h2>
      </div>
      <div className="grid gap-2">
        {days.map(({ day, title }) => (
          <div
            key={day}
            className="border border-dashed bg-brand/1 border-brand rounded-lg p-4 flex items-start gap-4"
          >
            <div className="text-base font-bold text-dark">Day {day}</div>
            <p className="text-dark/80">{title}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ItinerarySummary;

// "use client"

// import React from "react"
// import { List } from "lucide-react"

// export interface DaySummary {
//     day: number
//     title: string
// }

// interface ItinerarySummaryProps {
//     days: DaySummary[]
// }

// const ItinerarySummary: React.FC<ItinerarySummaryProps> = ({ days }) => {
//     return (
//         <section className="mt-12">
//             <div className="flex items-center gap-3 mb-6">
//                 <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//                     <List size={16} className="text-light" />
//                 </div>
//                 <h2 className="text-2xl md:text-3xl font-bold text-brand">
//                     Dhorpatan Trek - 11 Days Short Itinerary
//                 </h2>
//             </div>
//             <div className="grid gap-2">
//                 {days.map(({ day, title }) => (
//                     <div
//                         key={day}
//                         className="border border-dashed bg-brand/1 border-brand rounded-lg p-4 flex items-start gap-4"
//                     >
//                         <div className="flex flex-row">
//                             <div className="text-base font-bold text-dark">Day {day}</div>
//                         </div>
//                         <p className="text-dark/80">{title}</p>
//                     </div>
//                 ))}
//             </div>
//         </section>
//     )
// }

// export default ItinerarySummary

// 'use client';

// import React from 'react';
// import { List } from 'lucide-react';
// import { IPackageShortItinerary } from '@/types/package';

// export interface DaySummary {
//   day: number;
//   title: string;
// }

// interface ItinerarySummaryProps {
//   itinerary?: IPackageShortItinerary;
// }

// const ItinerarySummary: React.FC<ItinerarySummaryProps> = ({ itinerary }) => {
//   return (
//     <section className="mt-12">
//       <div className="flex items-center gap-3 mb-6">
//         <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//           <List size={16} className="text-light" />
//         </div>
//         <h2 className="text-2xl md:text-3xl font-bold text-brand">
//           {itinerary?.title}
//         </h2>
//       </div>
//       <div className="grid gap-2">
//         {itinerary?.points.map((point, index) => (
//           <div
//             key={`${itinerary.packageId}-${index}-${point}`}
//             className="border border-dashed bg-brand/1 border-brand rounded-lg p-4 flex items-start gap-4"
//           >
//             <div className="text-base font-bold text-dark">Day {index + 1}</div>
//             <p className="text-dark/80">{point}</p>
//           </div>
//         ))}
//       </div>
//     </section>
//   );
// };

// export default ItinerarySummary;

// 'use client';

// import React from 'react';
// import { List } from 'lucide-react';

// export interface DaySummary {
//   day: number;
//   title: string;
// }

// interface ItinerarySummaryProps {
//   days: DaySummary[];
// }

// const ItinerarySummary: React.FC<ItinerarySummaryProps> = ({ days }) => {
//   return (
//     <section className="mt-12">
//       <div className="flex items-center gap-3 mb-6">
//         <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//           <List size={16} className="text-light" />
//         </div>
//         <h2 className="text-2xl md:text-3xl font-bold text-brand">
//           Dhorpatan Trek - 11 Days Short Itinerary
//         </h2>
//       </div>
//       <div className="grid gap-2">
//         {days.map(({ day, title }) => (
//           <div
//             key={day}
//             className="border border-dashed bg-brand/1 border-brand rounded-lg p-4 flex items-start gap-4"
//           >
//             <div className="flex flex-row">
//               <div className="text-base font-bold text-dark">Day {day}</div>
//             </div>
//             <p className="text-dark/80">{title}</p>
//           </div>
//         ))}
//       </div>
//     </section>
//   );
// };

// export default ItinerarySummary;
