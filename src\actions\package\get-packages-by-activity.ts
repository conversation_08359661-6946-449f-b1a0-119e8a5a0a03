import { IApiResponse } from '@/types/response';
import { IPackage } from '@/types/package';

export const revalidate = 0;

export default async function getPackagesByActivitySlug(slug: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/activity-slug/${slug}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}

export async function getPackagesByActivityId(activityId: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/activity/${activityId}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}
