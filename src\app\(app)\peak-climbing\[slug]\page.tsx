import getPackage from '@/actions/package/get-package';
import getPackageSeo from '@/actions/package/get-package-seo';
import { JsonLd } from '@/modules/seo/json-ld';
import { createMetadata } from '@/modules/seo/metadata';
import TrekDetailPage from '@/modules/trekking/templates';

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ slug: string }>;
}) => {
  const { slug } = await params;
  const res = await getPackageSeo(slug);

  return createMetadata({
    title: res.data?.metaTitle || 'Trekking in Nepal - North Nepal Treks',
    description:
      res.data?.metaDescription || 'Trekking in Nepal - North Nepal Treks',
    ...res.data,
  });
};

export default async function page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const package_ = await getPackage(slug);

  return (
    <>
      <JsonLd code={package_.data.seo?.structuredData || {}} />
      <TrekDetailPage data={package_.data} />
    </>
  );
}
