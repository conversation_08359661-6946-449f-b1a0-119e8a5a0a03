import { IRegion } from '@/types/package';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

const fetchRegions = async (): Promise<IApiResponse<IRegion[]>> => {
  const response = await fetch('https://api.trailandtreknepal.com/region', {
    mode: 'cors',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch regions');
  }

  return data;
};

const fetchRegionsByActivitySlug = async (
  activitySlug: string
): Promise<IApiResponse<IRegion[]>> => {
  const response = await fetch(
    `https://api.trailandtreknepal.com/region/activity/${activitySlug}`,
    {
      mode: 'cors',
    }
  );
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch regions');
  }

  return data;
};

export const useRegions = () => {
  return useQuery({
    queryKey: ['regions'],
    queryFn: fetchRegions,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export const useRegionsByActivitySlug = (activitySlug: string) => {
  return useQuery({
    queryKey: ['regions', activitySlug],
    queryFn: () => fetchRegionsByActivitySlug(activitySlug),
    enabled: !!activitySlug,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
