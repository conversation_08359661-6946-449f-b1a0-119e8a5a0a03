import { <PERSON><PERSON> } from '@/components/ui/button';
import { Compass, Home, MapPin, Mountain, TreePine } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

const Page404 = () => {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-t from-background via-secondary/20 to-accent/30 px-4">
            <div className="max-w-2xl mx-auto text-center">
                {/* Animated 404 with Mountain Icon */}
                <div className="mb-8 animate-fade-in-up">
                    <div className="relative inline-block">
                        <div className="text-9xl font-bold text-brand select-none animate-bounce-gentle">
                            404
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <Mountain className="w-16 h-16 text-secondary animate-float" />
                        </div>
                    </div>
                </div>

                {/* Error Message */}
                <div className="mb-8 animate-fade-in-up delay-200">
                    <h1 className="text-3xl font-bold text-foreground mb-4">
                        Oops! Trail Not Found
                    </h1>
                    <p className="text-lg text-muted-foreground mb-2">
                        It looks like you&apos;re lost in the wilderness! You&apos;ve wandered off the beaten path. The page you&apos;re looking for doesn&apos;t exist,
                        but don&apos;t worry - there are plenty of amazing trails to explore!
                    </p>
                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                        <TreePine className="w-4 h-4" />
                        <span>Let&apos;s get you back on track</span>
                        <Compass className="w-4 h-4" />
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fade-in-up delay-400">
                    <Button size="lg" className='text-white'>
                        <Link
                            href="/"
                            className="flex items-center gap-2">
                            <Home className="w-4 h-4" />
                            Go to Home
                        </Link>
                    </Button>
                    <Button variant="outline" size="lg" className='text-white bg-secondary hover:bg-secondary/80 hover:text-white'>
                        <Link
                            href="/trekking"
                            className="flex items-center gap-2">
                            <MapPin className="w-4 h-4" />
                            Explore Trails   
                        </Link>
                    </Button>
                </div>

                {/* Decorative Elements */}
                <div className="flex justify-center gap-8 opacity-20">
                    <TreePine className="w-8 h-8 text-forest-green animate-float delay-1000" />
                    <Mountain className="w-10 h-10 text-mountain-blue animate-bounce-gentle delay-500" />
                    <TreePine className="w-6 h-6 text-forest-green animate-float delay-1500" />
                </div>
            </div>
        </div>
    )
}

export default Page404