import React from 'react';
import { Navigation } from 'lucide-react';

const ItineraryShortTable = () => {
    const itineraryData = [
        {
            day: 1,
            title: "Drive to Chame",
            distance: "271 km total drive",
            duration: "9-13.5 hours",
            altitudes: "Pokhara: 820m • Kathmandu: 1,400m • Besisahar: 760m • Chame: 2,650m"
        },
        {
            day: 2,
            title: "Fastpack from Chame to Manang",
            distance: "25 km",
            duration: "7-8 hours",
            altitudes: "Chame: 2,650m • Manang: 3,519m"
        },
        {
            day: 3,
            title: "Run from Manang to Thorung Phedi",
            distance: "18 km",
            duration: "6-7 hours",
            altitudes: "Manang: 3,519m • Thorung Phedi: 4,540m"
        },
        {
            day: 4,
            title: "Thorung Phedi to Muktinath via Thorung La Pass",
            distance: "17 km",
            duration: "6-7 hours",
            altitudes: "Thorung Phedi: 4,540m • Thorung La Pass: 5,416m • Muktinath: 3,710m"
        },
        {
            day: 5,
            title: "Drive from Muktinath to Pokhara",
            distance: "174 km drive",
            duration: "6-7 hours",
            altitudes: "Muktinath: 3,710m • Pokhara: 820m"
        }
    ];

    return (
        <div className="mt-6 md:mt-12">
            <div className="flex items-center gap-2 md:gap-3 mb-4 md:mb-8">
                <div className="w-6 h-6 md:w-8 md:h-8 bg-primary rounded-full flex items-center justify-center">
                    <Navigation size={12} className="md:w-4 md:h-4 text-light" />
                </div>
                <h2 className="text-xl md:text-3xl font-bold text-primary">Shorter Itinerary</h2>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full border-collapse bg-light rounded-lg shadow-sm border border-primary/50 text-xs md:text-sm md:table-fixed">
                    <thead>
                        <tr className="bg-primary/80">
                            <th className="border border-light px-1 py-1 md:px-2 md:py-2 text-left font-semibold text-light w-8 md:w-16">Day</th>
                            <th className="border border-light px-1 py-1 md:px-2 md:py-2 text-left font-semibold text-light md:w-80">Route</th>
                            <th className="border border-light px-1 py-1 md:px-2 md:py-2 text-left font-semibold text-light w-16 md:w-32">Distance</th>
                            <th className="border border-light px-1 py-1 md:px-2 md:py-2 text-left font-semibold text-light w-14 md:w-28">Duration</th>
                            <th className="border border-light px-1 py-1 md:px-2 md:py-2 text-left font-semibold text-light w-32 md:w-52">Altitudes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {itineraryData.map((day) => (
                            <tr key={day.day} className="hover:bg-gray-50 transition-colors">
                                <td className="border border-primary/50 px-1 py-1 md:px-2 md:py-2">
                                    <div className="flex flex-col items-center">
                                        <span className="text-xs text-primary font-medium hidden md:block">Day</span>
                                        <span className="text-lg md:text-xl font-bold text-primary">{day.day}</span>
                                    </div>
                                </td>
                                <td className="border border-primary/50 px-1 py-1 md:px-2 md:py-2">
                                    <div className="space-y-2">
                                        <h3 className="font-semibold text-gray-900 text-xs md:text-sm leading-tight">{day.title}</h3>
                                    </div>
                                </td>
                                <td className="border border-primary/50 px-1 py-1 md:px-2 md:py-2">
                                    <div className="flex items-center gap-2">
                                        <span className="text-xs md:text-sm font-medium">{day.distance}</span>
                                    </div>
                                </td>
                                <td className="border border-primary/50 px-1 py-1 md:px-2 md:py-2">
                                    <div className="flex items-center gap-2">
                                        <span className="text-xs md:text-sm font-medium">{day.duration}</span>
                                    </div>
                                </td>
                                <td className="border border-primary/50 px-1 py-1 md:px-2 md:py-2">
                                    <div className="text-xs md:text-sm text-gray-600 leading-tight md:leading-relaxed md:space-y-1">
                                        {day.altitudes.split(' • ').map((altitude, index) => (
                                            <div key={index} className="flex items-center gap-1">
                                                <span>{altitude}</span>
                                            </div>
                                        ))}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default ItineraryShortTable;