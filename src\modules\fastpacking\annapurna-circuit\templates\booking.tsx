import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

const BookingInterface = () => {
    const [selectedYear, setSelectedYear] = useState('2025');
    const [selectedMonth, setSelectedMonth] = useState('Aug');
    const [selectedDate, setSelectedDate] = useState<number | null>(null);
    const [timeLeft, setTimeLeft] = useState({
        days: 6,
        hours: 17,
        minutes: 20,
        seconds: 0
    });

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft(prev => {
                if (prev.seconds > 0) {
                    return { ...prev, seconds: prev.seconds - 1 };
                } else if (prev.minutes > 0) {
                    return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
                } else if (prev.hours > 0) {
                    return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
                } else if (prev.days > 0) {
                    return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 };
                }
                return prev;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const groupDepartures = [
        { id: 1, startDate: 'Monday\nAug 4, 2025', endDate: 'Saturday\nAug 16, 2025', price: 1081, originalPrice: 1291, discount: 210, available: 10 },
        { id: 2, startDate: 'Friday\nAug 8, 2025', endDate: 'Wednesday\nAug 20, 2025', price: 1081, originalPrice: 1291, discount: 210, available: 10 },
        { id: 3, startDate: 'Tuesday\nAug 12, 2025', endDate: 'Sunday\nAug 24, 2025', price: 1081, originalPrice: 1291, discount: 210, available: 10 },
        { id: 4, startDate: 'Saturday\nAug 16, 2025', endDate: 'Thursday\nAug 28, 2025', price: 1081, originalPrice: 1291, discount: 210, available: 10 },
        { id: 5, startDate: 'Wednesday\nAug 20, 2025', endDate: 'Monday\nSep 1, 2025', price: 1081, originalPrice: 1291, discount: 210, available: 10 }
    ];

    const months = ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const julyDates = [
        [8, 9, 10, 11, 12],
        [13, 14, 15, 16, 17],
        [18, 19, 20, 21, 22],
        [23, 24, 25, 26, 27],
        [28, 29, 30, 31]
    ];

    const handleDateSelect = (date: number) => {
        setSelectedDate(date);
    };

    const handleBookNow = () => {
        alert('Redirecting to booking confirmation...');
    };

    return (
        <div className="w-full container mx-auto p-6 bg-secondary/10">
            <div className="relative mb-2">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-800 py-8 relative">
                    Booking                                </h2>
                <div className="absolute bottom-6 w-15 rounded-full h-1 bg-primary"></div>
            </div>

            <div className="mb-6 space-y-3">
                <p className="text-sm text-gray-600">
                    The &quot;Fixed Departure&quot; tab is for joining pre-scheduled group departures. It shows available dates for group trips, and you can book your spot. Contact us if your preferred date isn&apos;t listed.
                </p>
                <p className="text-sm text-gray-600">
                    The &quot;Private Trip&quot; tab is for personalized experiences. Book a private trek tailored to your schedule and preferences.
                </p>
            </div>

            <Tabs defaultValue="group" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="group">Group Departures</TabsTrigger>
                    <TabsTrigger value="private">Private Trip</TabsTrigger>
                </TabsList>

                <div className="flex justify-center my-6">
                    <div className="flex rounded-lg overflow-hidden">
                        <button
                            onClick={() => setSelectedYear('2025')}
                            className={`px-6 py-2 font-semibold ${selectedYear === '2025'
                                    ? 'bg-primary text-white'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                }`}
                        >
                            2025
                        </button>
                        <button
                            onClick={() => setSelectedYear('2026')}
                            className={`px-6 py-2 font-semibold ${selectedYear === '2026'
                                    ? 'bg-primary text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-300'
                                }`}
                        >
                            2026
                        </button>
                    </div>
                </div>

                <div className="mb-6">
                    <h3 className="text-center font-semibold text-gray-700 mb-4">
                        Available months in {selectedYear}
                    </h3>
                    <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                        {months.map((month) => (
                            <button
                                key={month}
                                onClick={() => setSelectedMonth(month)}
                                className={`py-2 px-4 font-medium rounded transition-colors ${selectedMonth === month
                                        ? 'bg-primary text-white'
                                        : 'bg-white text-gray-700 hover:bg-gray-300'
                                    }`}
                            >
                                {month}
                            </button>
                        ))}
                    </div>
                </div>

                <TabsContent value="group">
                    {/* Countdown Timer */}
                    <div className="bg-red/10 border-l-4 border-red p-4 mb-6 rounded">
                        <div className="flex items-center">
                            <Clock className="w-5 h-5 text-red mr-2" />
                            <span className="text-red font-medium">
                                All Departure Date Guarantee to Run. Hurry Up ! The deals end in{' '}
                                <span className="font-bold">
                                    {timeLeft.days}d {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
                                </span>
                            </span>
                        </div>
                    </div>

                    {/* Group Departures List */}
                    <div className="space-y-4">
                        {groupDepartures.map((departure) => (
                            <div key={departure.id} className="border rounded-lg p-4 bg-gray-50">
                                <div className="flex items-center justify-between">
                                    <div className="bg-secondary text-white px-3 py-1 rounded text-sm font-medium">
                                        ⚡ Get Instant Confirmation
                                    </div>
                                    <div className="bg-red text-white px-3 py-1 rounded text-sm font-medium">
                                        ${departure.discount} off
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 items-center">
                                    <div className="flex items-center">
                                        <div className="text-sm text-gray-600 whitespace-pre-line">
                                            {departure.startDate}
                                        </div>
                                        <div className="mx-4">
                                            <div className="w-8 h-px bg-gray-300"></div>
                                        </div>
                                        <div className="text-sm text-gray-600 whitespace-pre-line">
                                            {departure.endDate}
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-800">
                                            USD ${departure.price}
                                        </div>
                                        <div className="text-sm text-gray-500 line-through">
                                            USD ${departure.originalPrice}
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <div className="text-sm text-gray-600">
                                            {departure.available} LEFT
                                        </div>
                                        <div className="flex items-center justify-center text-green-600 text-sm">
                                            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                                            Available
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <button
                                            onClick={handleBookNow}
                                            className="bg-primary hover:bg-cyan-600 text-white font-semibold py-2 px-6 rounded transition-colors"
                                        >
                                            Book Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="private">
                    {/* Date Selection Calendar */}
                    <div className="mb-6">
                        <h3 className="text-center font-semibold text-gray-700 mb-4">
                            Select a Departure Date
                        </h3>
                        <div className="grid grid-cols-5 gap-2">
                            {julyDates.flat().map((date) => (
                                <button
                                    key={date}
                                    onClick={() => handleDateSelect(date)}
                                    className={`py-3 px-4 rounded font-medium transition-colors ${selectedDate === date
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    Jul {date}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Selected Date Booking */}
                    {selectedDate && (
                        <div className="border rounded-lg p-4 bg-gray-50">
                            <div className="flex items-center justify-between mb-4">
                                <div className="bg-orange-400 text-white px-3 py-1 rounded text-sm font-medium">
                                    ⚡ Get Instant Confirmation
                                </div>
                                <div className="bg-red-500 text-white px-3 py-1 rounded text-sm font-medium">
                                    $187 off
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                                <div className="flex items-center">
                                    <div className="text-sm text-gray-600">
                                        Tuesday<br />Jul {selectedDate}, 2025
                                    </div>
                                    <div className="mx-4">
                                        <div className="w-8 h-px bg-gray-300"></div>
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Sunday<br />Jul {selectedDate + 12}, 2025
                                    </div>
                                </div>

                                <div className="text-center">
                                    <div className="text-2xl font-bold text-gray-800">
                                        USD $1081
                                    </div>
                                    <div className="text-sm text-gray-500 line-through">
                                        USD $1268
                                    </div>
                                </div>

                                <div className="text-center">
                                    <div className="text-sm text-gray-600">13 days</div>
                                </div>

                                <div className="text-center">
                                    <button
                                        onClick={handleBookNow}
                                        className="bg-primary hover:bg-cyan-600 text-white font-semibold py-2 px-6 rounded transition-colors"
                                    >
                                        Book Now
                                    </button>
                                </div>
                            </div>

                            <div className="mt-4 text-sm text-red-600">
                                Get Instant Booking Confirmation
                            </div>
                        </div>
                    )}
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default BookingInterface;