'use client';
import { IHomeHiking } from '@/types/home';
import Image from 'next/image';
import Link from 'next/link';

export default function HikingAreasSection({
  hiking,
}: {
  hiking: IHomeHiking;
}) {
  return (
    <section className="py-8 md:py-16 bg-gray-50">
      <div className="container mx-auto px-4 max-w-6xl">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-16">
          {hiking.heading}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {hiking.areas.map((area) => (
            <Link
              key={area.id}
              href={area.linkUrl}
              className="group block relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="relative h-72 overflow-hidden">
                <Image
                  src={area.image || '/placeholder.svg'}
                  alt={area.title}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
              </div>

              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <h3 className="text-xl font-bold mb-2 group-hover:text-secondary transition-colors">
                  {area.title}
                </h3>
                <p className="text-gray-200 text-sm leading-relaxed">
                  {area.subtitle}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

// "use client"

// import React from "react"
// import Image from "next/image"
// import Link from "next/link"

// const AREAS = [
//   {
//     id: 1,
//     title: "Annapurna Base Camp (ABC)",
//     description: "Iconic trek through diverse landscapes",
//     image: "/images/home/<USER>",
//     href: "/areas/1",
//   },
//   {
//     id: 2,
//     title: "Manaslu Circuit Trek",
//     description: "Short and scenic trek with panoramic views",
//     image: "/images/home/<USER>",
//     href: "/areas/2",
//   },
//   {
//     id: 3,
//     title: "Everest Base Camp Trek",
//     description: "Popular for sunrise views of Annapurna & Dhaulagiri",
//     image: "/images/home/<USER>",
//     href: "/areas/3",
//   },
//   {
//     id: 4,
//     title: "Langtang Ridge Trek",
//     description: "Less crowded alternative to Poon Hill",
//     image: "/images/home/<USER>",
//     href: "/areas/4",
//   },
// ]

// export default function HikingAreasSection() {
//   return (
//     <section className="mb-24 md:mb-16 bg-gray-50">
//       <div className="container mx-auto px-4">
//         <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-12">
//           Explore our Hiking Areas – Nepal
//         </h2>

//         <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
//           {AREAS.slice(0, 4).map((area) => (
//             <Link
//               key={area.id}
//               href={area.href}
//               className="block relative overflow-hidden rounded-lg shadow-lg group"
//             >
//               <div className="relative h-64 w-full overflow-hidden">
//                 <Image
//                   src={area.image}
//                   alt={area.title}
//                   fill
//                   className="object-cover w-full h-56 transition-transform duration-300 group-hover:scale-105"
//                 />
//               </div>
//               <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-60 transition-opacity" />
//               <div className="absolute bottom-4 left-4 right-4 text-dark">
//                 <h3 className="text-lg font-semibold">{area.title}</h3>
//                 <p className="text-sm">{area.description}</p>
//               </div>
//             </Link>
//           ))}
//         </div>
//       </div>
//     </section>
//   )
// }
