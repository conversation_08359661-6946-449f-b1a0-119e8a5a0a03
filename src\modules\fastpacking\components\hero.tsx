import TrailRunningDivider from '@/components/divider/trailrunning'
import Image from 'next/image'
import React from 'react'

interface HeroSectionProps {
    imageSrc: string
    imageAlt: string
    title: string
}

const HeroSection: React.FC<HeroSectionProps> = ({
    imageSrc,
    imageAlt,
    title
}) => {
    return (
        <div className="relative md:h-screen h-[500px] min-h-[300px] overflow-hidden ">
            <div className="absolute inset-0 transition-opacity duration-1000 ease-in-out">
                <div className="relative w-full h-full">
                    <Image
                        src={imageSrc}
                        alt={imageAlt}
                        fill
                        className="w-full h-full object-cover"
                        priority
                    />
                </div>
            </div>

            <div className="absolute inset-0 bg-black/10"></div>

            <TrailRunningDivider />

            <div className="relative z-10 flex h-full items-end justify-start pb-16 md:pb-24">
                <div className="container mx-auto px-4 text-left">
                    <h1 className="text-3xl md:text-6xl font-bold text-light mb-10">
                        {title}
                    </h1>
                </div>
            </div>
        </div>
    )
}

export default HeroSection


// import Divider from '@/modules/home/<USER>/divider'
// import Image from 'next/image'
// import React from 'react'

// const HeroSection = () => {
//     return (
//         <div className="relative md:h-screen h-[500px] min-h-[300px] overflow-hidden ">
//             <div className="absolute inset-0">
//                 <div className="relative w-full h-full">
//                     <div className="absolute inset-0 transition-opacity duration-1000 ease-in-out">
//                         <div className='relative w-full h-full'>
//                             <Image
//                                 src="/images/fastpacking/annapurna-circuit/hero.jpeg"
//                                 alt="Annapurna Circuit Fastpacking"
//                                 fill
//                                 className="w-full h-full object-cover"
//                                 priority
//                             />
//                         </div>
//                     </div>
//                 </div>
//             </div>

//             <div className="absolute inset-0 bg-black/10"></div>
//             <Divider />

//             <div className="relative z-10 flex h-full items-end justify-start pb-16 md:pb-24">
//                 <div className="container mx-auto px-4 text-left">
//                     <h1 className="text-3xl md:text-6xl font-bold text-light mb-10">
//                         Annapurna Circuit Fastpacking
//                     </h1>
//                 </div>
//             </div>
//         </div>
//     )
// }

// export default HeroSection