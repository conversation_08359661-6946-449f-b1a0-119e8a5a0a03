'use client'

import React from 'react'
import { Map } from 'lucide-react'
import Image from 'next/image'

interface MapAndChartProps {
  title?: string
  imageSrc: string
  altText: string
}

const MapAndChart: React.FC<MapAndChartProps> = ({
  title = 'Map and Chart',
  imageSrc,
  altText,
}) => {
  return (
    <div>
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
          <Map size={16} className="text-light" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-brand">{title}</h2>
      </div>

      <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-8">
        <div className="">
            <div className="relative w-full h-[500px] md:h-[1200px] rounded-lg overflow-hidden bg-gray-50">
            <Image
              src={imageSrc}
              alt={altText}
              fill
              className="object-scale-down"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default MapAndChart

// import React from 'react'
// import { Map } from 'lucide-react';
// import Image from 'next/image';

// const Mapandchart = () => {
//     return (
//         <div className="">
//             <div className="flex items-center gap-3 mb-6">
//                 <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//                     <Map size={16} className="text-light" />
//                 </div>
//                 <h2 className="text-2xl md:text-3xl font-bold text-brand">Map and Chart</h2>
//             </div>
            
//             <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-8">
//                 <div className="">
//                     <div className="relative w-full h-70 md:h-[1200px] rounded-lg overflow-hidden bg-gray-50">
//                         <Image
//                             src="/images/fastpacking/annapurna-circuit/map.webp"
//                             alt="Annapurna Circuit Route Map showing the complete fastpacking trail with key landmarks, villages, and elevation points"
//                             fill
//                             className="object-scale-down"
//                         />
//                     </div>
//                 </div>
//             </div>
//         </div>
//         )
// }

// export default Mapandchart