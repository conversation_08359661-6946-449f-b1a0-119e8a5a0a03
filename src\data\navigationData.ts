// import { adventurePackages, heliTourPackages, peakClimbingPackages, tourPackages, trekkingPackages } from "./package";

// const createSlug = (title: string): string => {
//   return title
//     .toLowerCase()
//     .replace(/\s+/g, '-')
//     .replace(/[^\w-]/g, '')
//     .replace(/--+/g, '-')
//     .trim();
// };

// export const getNavigationData = () => {
//   const trekking = trekkingPackages.map(pkg => ({
//     title: pkg.name,
//     href: `/trekking/${createSlug(pkg.name)}`,
//     duration: pkg.duration,
//     price: pkg.price
//   }));

//   const peakClimbing = peakClimbingPackages.map(pkg => ({
//     title: pkg.title,
//     href: `/peak-climbing/${createSlug(pkg.title)}`,
//     duration: pkg.duration,
//     price: pkg.currentPrice
//   }));

//   const tours = tourPackages.map(pkg => ({
//     title: pkg.title,
//     href: `/tours/${createSlug(pkg.title)}`,
//     duration: pkg.duration,
//     price: pkg.currentPrice
//   }));

//   const adventure = adventurePackages.map(pkg => ({
//     title: pkg.title,
//     href: `/adventure/${createSlug(pkg.title)}`,
//     duration: pkg.duration,
//     price: pkg.currentPrice
//   }));

//   const heliTour = heliTourPackages.map(pkg => ({
//     title: pkg.title,
//     href: `/heli-tour/${createSlug(pkg.title)}`,
//     duration: pkg.duration,
//     price: pkg.currentPrice
//   }));

//   const destinations = [
//     { title: "Everest Region", href: "/destinations/everest-region", description: "Home to the world's highest peak" },
//     { title: "Annapurna Region", href: "/destinations/annapurna-region", description: "Diverse landscapes and cultures" },
//     { title: "Langtang Region", href: "/destinations/langtang-region", description: "Close to Kathmandu valley" },
//     { title: "Manaslu Region", href: "/destinations/manaslu-region", description: "Remote and pristine trails" },
//   ];

//   return {
//     trekking,
//     peakClimbing,
//     tours,
//     adventure,
//     heliTour,
//     destinations
//   };
// };

// export const navigationMenuData = getNavigationData();

// export const getAllPackageTitles = () => {
//   const allPackages = [
//     ...trekkingPackages,
//     ...peakClimbingPackages,
//     ...tourPackages,
//     ...adventurePackages,
//     ...heliTourPackages
//   ];

//   return Array.from(new Set(allPackages.map(pkg => pkg.name)));
// };

// export const navigationItemsWithData = [
//   {
//     title: "TREKKING",
//     href: "/trekking",
//     hasDropdown: true,
//     items: navigationMenuData.trekking
//   },
//   {
//     title: "PEAK CLIMBING",
//     href: "/peak-climbing",
//     hasDropdown: true,
//     items: navigationMenuData.peakClimbing
//   },
//   {
//     title: "TOURS",
//     href: "/tours",
//     hasDropdown: true,
//     items: navigationMenuData.tours
//   },
//   {
//     title: "ADVENTURE",
//     href: "/adventure",
//     hasDropdown: true,
//     items: navigationMenuData.adventure
//   },
//   {
//     title: "DESTINATIONS",
//     href: "/destinations",
//     hasDropdown: true,
//     items: navigationMenuData.destinations
//   },
//   {
//     title: "HELI TOUR",
//     href: "/heli-tour",
//     hasDropdown: true,
//     items: navigationMenuData.heliTour
//   },
//   {
//     title: "BLOGS",
//     href: "/blogs",
//     hasDropdown: false,
//     items: []
//   },
// ];
