import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { MapPin, Clock, Navigation, Car } from 'lucide-react';
import React from 'react'

const ItineraryMansalu = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <MapPin size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-brand">Manaslu Circuit Fastpacking Itinerary</h2>
            </div>

            <Accordion type="multiple" className="space-y-4">
                {/* Day 1 - From the second image */}
                <AccordionItem value="day-1">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">1</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Arrival and Initial Trek
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>~10 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>2-4 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '10%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Initial trekking day with mixed transportation and hiking.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 2 - From the second image */}
                <AccordionItem value="day-2">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">2</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Dinner to Preax
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>22 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>7-8 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '25%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Long trekking day through varied terrain.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 3 - From the second image */}
                <AccordionItem value="day-3">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">3</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Preax to Live
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>21-27 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-8 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '40%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Challenging trek with options for different routes through Team Valley.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 4 - From the second image */}
                <AccordionItem value="day-4">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">4</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Live to Samagam and Manaslu Base Camp
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>21 km total</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>11-17 hours total</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '55%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Live: 3,800 m</div>
                                    <div>Samagam: 3,500 m</div>
                                    <div>Manaslu BC: 4,800 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Long day including trek to Samagam and optional excursion to Manaslu Base Camp.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 5 - From first image (Day 6) */}
                <AccordionItem value="day-5">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">5</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Dharmathaka to Bhimizang via Lariya La Pass
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>17 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '70%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Dharmathaka: 4,460 m</div>
                                    <div>Lariya La Pass: 5,106 m</div>
                                    <div>Bhimizang: 2,700 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Challenging day crossing the high Lariya La Pass with significant elevation changes.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 6 - From first image (Day 7) */}
                <AccordionItem value="day-6">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">6</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Bhimizang to Dharapani
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>25 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '85%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Bhimizang: 2,700 m</div>
                                    <div>Dharapani: 1,870 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Long descent day through changing landscapes and villages.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 7 - From first image (Day 8) */}
                <AccordionItem value="day-7">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">7</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Return to Pokhara
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Driving Distance: <strong>~326 km total</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>11-15 hours total</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                            <div className="text-dark/85 leading-relaxed space-y-3">
                                <p><strong>Route Segments:</strong></p>
                                <ul className="list-disc list-inside space-y-2">
                                    <li>Dharganti to Belisabad: 46 km (3 hours)</li>
                                    <li>Belisabad to Latembara: 175 km (4-7 hours)</li>
                                    <li>Belisabad to Pokhara: 105 km (4-5 hours)</li>
                                </ul>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
}

export default ItineraryMansalu