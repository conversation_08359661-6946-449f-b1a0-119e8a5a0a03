// import PricingCard from '@/components/common/sidebar/pricingcard';
// import TrekInfo from '@/components/common/trek-overview/trek';
// import Gears from '@/modules/fastpacking/components/gears';
// import Highlight from '@/modules/fastpacking/components/highlight';
// import Mapandchart from '@/modules/fastpacking/components/map';
// import Overview from '@/modules/fastpacking/components/overview';
// import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section';
// import PackageInformation from '@/modules/fastpacking/components/detail';
// import FAQSection from '@/modules/fastpacking/components/faq-section';
// import HeroSection from '@/modules/fastpacking/components/hero';
// import TrekInclusionsExclusions from '@/modules/fastpacking/components/included-excluded';
// import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed';
// import ItinerarySummary from '@/modules/fastpacking/components/itinerary-summary';
// import StackedReviews from '@/modules/fastpacking/components/review';
// import TravelersReview from '@/modules/fastpacking/components/review-video';
// import React from 'react';
// import {
//   AlertTriangle,
//   Backpack,
//   Ban,
//   FileText,
//   HandCoins,
//   Hotel,
//   ShieldCheck,
//   UserCheck,
//   Utensils,
//   UtensilsCrossed,
// } from 'lucide-react';
// import { detailedDhaulagiriItinerary } from '@/data/trekking/dhaulagiri-circuit/itinerary-detailed';
// import { dhaulagiriSection } from '@/data/trekking/dhaulagiri-circuit/package-info';
// import { IPackage } from '@/types/package';

// const PackagePage = ({ package_ }: { package_: IPackage }) => {
//   return (
//     <>
//       <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
//         <HeroSection
//           imageSrc={package_.mainImage}
//           imageAlt={package_.mainImageAlt}
//           title={package_.name}
//         />
//       </div>

//       <div className="container mx-auto px-2 md:px-4 py-8">
//         <div className="flex flex-col lg:flex-row gap-8">
//           <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
//             <div className="sticky top-40 space-y-6">
//               <PricingCard
//                 duration="4 Days"
//                 originalPrice="USD 2000"
//                 currentPrice="USD 1889 pp"
//                 onInquireNow={() => {}}
//                 onDatesPrice={() => {}}
//                 onBookNow={() => {}}
//               />
//             </div>
//           </div>
//           <div className="order-2 lg:order-1 flex-1 space-y-8">
//             <div>
//               <TrekInfo
//                 trekData={{
//                   destination: package_.bestSeason,
//                   accommodation: package_.accomodation,
//                   type: package_.type,
//                   duration: package_.duration,
//                   maxElevation: package_.altitude,
//                   group: package_.groupSize,
//                   region: package_.region.name,
//                 }}
//               />
//             </div>

//             <hr className="w-full h-px bg-gray-200 border-0 my-2" />

//             <div>
//               <div className="prose prose-lg max-w-none">
//                 <div className="text-gray-700 text-lg leading-relaxed space-y-4">
//                   <p className="text-justify">{package_.overviewDescription}</p>
//                 </div>
//               </div>
//             </div>

//             <Highlight items={package_.highlights} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <Overview description={package_.description} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <ItinerarySummary itinerary={package_.shortItinerary} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <GalleryWithMore gallery={package_.gallery} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <ItineraryDetailed itinerary={package_.itinerary} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <Mapandchart
//               imageSrc="/images/map/trekking/dhaulagiri-circuit-trek.webp"
//               altText="Dhaulagiri Circuit Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
//             />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <TrekInclusionsExclusions
//               inclusions={package_.inclusions}
//               exclusions={package_.exclusions}
//             />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <Gears />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             {/* <PreperationandTraining />
//                         <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

//             <PackageInformation sections={dhaulagiriSection} />
//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

//             <TravelersReview
//               videos={sampleVideos}
//               onWatchMore={() =>
//                 window.open('https://youtube.com/yourchannel', '_blank')
//               }
//             />

//             <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
//             <StackedReviews
//               reviews={myReviews}
//               headerTitle="Customer Success Stories"
//               // headerIcon={SomeOtherIcon}
//               // containerHeightVH={80}
//               // stickyTopClass="top-32"
//             />
//           </div>
//         </div>
//       </div>
//       <FAQSection />
//     </>
//   );
// };

// export default PackagePage;

import React from 'react';

function PackagePage() {
  return <div>PackagePage</div>;
}

export default PackagePage;
