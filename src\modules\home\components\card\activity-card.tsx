"use client"

import React, { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"

export interface Stat {
    icon: React.ReactNode
    label: string
    value: string
}

export interface ActivityCategoryCardProps {
    slug: string
    title: string
    images: string[]
    stats: Stat[]
    index?: number
}

export function ActivityCategoryCard({
    slug,
    title,
    images,
    stats,
    index = 0,
}: ActivityCategoryCardProps) {

    const [current, setCurrent] = useState(0)
    const carouselRef = useRef<NodeJS.Timeout | null>(null)

    const startCarousel = () => {
        if (images.length < 2) return
        stopCarousel()
        carouselRef.current = setInterval(() => {
            setCurrent((c) => (c + 1) % images.length)
        }, 1500) // speed of rotation
    }

    const stopCarousel = () => {
        if (carouselRef.current) {
            clearInterval(carouselRef.current)
            carouselRef.current = null
        }
        setCurrent(0)
    }

    useEffect(() => {
        return () => stopCarousel()
    }, [])


    return (
        <div
            role="listitem"
            onMouseEnter={startCarousel}
            onMouseLeave={stopCarousel}
            className="snap-center md:snap-auto relative h-[460px] rounded-lg overflow-hidden bg-neutral-200 shadow-xl "
        >
            <Link
                href={`/${slug}`}
                aria-label={title}
                className="absolute inset-0 z-10 focus:outline-none focus-visible:ring-4 focus-visible:ring-white/60 focus-visible:rounded-3xl"
            />

            <div
                className="flex h-full transition-transform duration-700 ease-in-out"
                style={{ transform: `translateX(-${current * 100}%)` }}
            >
                {images.map((img, i) => (
                    <div key={i} className="relative min-w-full h-full">
                        <Image
                            src={img}
                            alt={title}
                            fill
                            priority={index < 2}
                            className="object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                ))}
            </div>

            {/* Content */}
            <div className="absolute inset-0 flex flex-col justify-between p-6 z-20">
                <h3 className="text-white text-2xl font-bold text-center drop-shadow-lg">
                    {title}
                </h3>
                <div className="flex-1" />

                {/* stats grid */}
                <div className="grid grid-cols-2 gap-y-1 gap-x-4 mb-2">
                    {stats.map((s, si) => (
                        <div
                            key={si}
                            className={`flex items-center gap-1 bg-black/50 backdrop-blur-sm rounded-xl px-2 py-1 ${si % 2 === 1 ? "justify-self-end" : "justify-self-start"}`}
                        >
                            <span className="text-white">{s.icon}</span>
                            <p className="text-xs font-medium text-white whitespace-nowrap">
                                {s.value}
                            </p>
                        </div>
                    ))}
                </div>

                <Link
                    href={`/${slug}`}
                    className="block w-full text-center bg-white text-gray-900 font-semibold rounded-lg py-2"
                >
                    Find Your {title}
                </Link>
            </div>
        </div>
    )
}
