import React from "react";
import { Navigation, Clock, Mountain } from "lucide-react";
import { DayDetail } from "@/modules/fastpacking/components/itinerary-detailed";

export const detailedManasluCircuitItinerary: DayDetail[] = [
  {
    day: 1,
    title: "Arrival in Kathmandu",
    stats: [],
    progressPct: 10,
    description: (
      <>
        <p>
          After your arrival in Kathmandu, one of our representatives will pick you up at the airport and transfer you to your hotel. Take this day to rest and prepare for the adventure ahead.
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,324 m" }],
  },
  {
    day: 2,
    title: "Drive from Kathmandu to Machha Khola",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive Duration", value: "8–10 hrs" },
    ],
    progressPct: 20,
    description: (
      <>
        <p>
          Drive westward from Kathmandu along the Budhi Gandaki River, passing through diverse villages and scenic countryside until you reach Machha Khola.
        </p>
      </>
    ),
    altitudes: [
      { label: "Kathmandu", value: "1,324 m" },
      { label: "Machha Khola", value: "870 m" },
    ],
  },
  {
    day: 3,
    title: "Trek from Machha Khola to Jagat",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "22 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "7 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "430 m" },
    ],
    progressPct: 30,
    description: (
      <>
        <p>
          Follow a narrow trail along the Budhi Gandaki River, crossing suspension bridges and passing small settlements and terraced fields to reach Jagat.
        </p>
      </>
    ),
    altitudes: [
      { label: "Machha Khola", value: "870 m" },
      { label: "Jagat", value: "1,300 m" },
    ],
  },
  {
    day: 4,
    title: "Jagat to Deng",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "20 km approx" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "6–7 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "500 m" },
    ],
    progressPct: 38,
    description: (
      <>
        <p>
          Ascend and descend through forests and stone steps while enjoying views of waterfalls and crossing several suspension bridges to reach Deng.
        </p>
      </>
    ),
    altitudes: [
      { label: "Jagat", value: "1,300 m" },
      { label: "Deng", value: "1,800 m" },
    ],
  },
  {
    day: 5,
    title: "Deng to Namrung",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "19.5 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "6–7 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "860 m" },
    ],
    progressPct: 46,
    description: (
      <>
        <p>
          Trek through pine and rhododendron forests, pass by traditional villages like Ghap and Prok, and enjoy glimpses of snow-covered peaks as you ascend to Namrung.
        </p>
      </>
    ),
    altitudes: [
      { label: "Deng", value: "1,800 m" },
      { label: "Namrung", value: "2,660 m" },
    ],
  },
  {
    day: 6,
    title: "Namrung to Samagaon",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "17.4 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "6–7 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "890 m" },
    ],
    progressPct: 54,
    description: (
      <>
        <p>
          Walk through alpine forest, past Tibetan-style villages like Lihi, Sho, and Lho. Reach the scenic and culturally rich village of Samagaon surrounded by mountain peaks.
        </p>
      </>
    ),
    altitudes: [
      { label: "Namrung", value: "2,660 m" },
      { label: "Samagaon", value: "3,530 m" },
    ],
  },
  {
    day: 7,
    title: "Acclimatization Day at Samagaon",
    stats: [],
    progressPct: 60,
    description: (
      <>
        <p>
          Spend the day acclimatizing to the altitude. Optional hikes to surrounding areas or monasteries to aid acclimatization.
        </p>
      </>
    ),
    altitudes: [{ label: "Samagaon", value: "3,530 m" }],
  },
  {
    day: 8,
    title: "Samagaon to Samdo",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "16.4 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "4–5 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "160 m" },
    ],
    progressPct: 68,
    description: (
      <>
        <p>
          Follow the Budhi Gandaki River to reach Samdo, a small settlement with a strong Tibetan influence near the Tibetan border.
        </p>
      </>
    ),
    altitudes: [
      { label: "Samagaon", value: "3,530 m" },
      { label: "Samdo", value: "3,690 m" },
    ],
  },
  {
    day: 9,
    title: "Samdo to Dharamsala",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "11.7 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "5–6 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Gain", value: "770 m" },
    ],
    progressPct: 76,
    description: (
      <>
        <p>
          Gradual ascent through high-altitude yak pastures and remote terrain to reach Dharamsala, the base for crossing Larke Pass.
        </p>
      </>
    ),
    altitudes: [
      { label: "Samdo", value: "3,690 m" },
      { label: "Dharamsala", value: "4,460 m" },
    ],
  },
  {
    day: 10,
    title: "Dharamsala to Bhimtang via Larke La Pass",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "24.7 km approx" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "8–9 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Loss", value: "740 m" },
    ],
    progressPct: 84,
    description: (
      <>
        <p>
          Begin the day early to cross Larke La Pass (5,106 m), the highest point of the trek. Descend through snowfields and rocky terrain to reach the lush valley of Bhimtang.
        </p>
      </>
    ),
    altitudes: [
      { label: "Larke La Pass", value: "5,106 m" },
      { label: "Bhimtang", value: "3,720 m" },
    ],
  },
  {
    day: 11,
    title: "Bhimtang to Dharapani",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "20 km approx" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7–8 hrs" },
      { icon: <Mountain size={16} className="text-dark/80" />, label: "Elevation Loss", value: "1,860 m" },
    ],
    progressPct: 92,
    description: (
      <>
        <p>
          Descend through rhododendron and pine forests, passing small villages like Tilije and crossing suspension bridges as the trail becomes more lush and temperate.
        </p>
      </>
    ),
    altitudes: [
      { label: "Bhimtang", value: "3,720 m" },
      { label: "Dharapani", value: "1,860 m" },
    ],
  },
  {
    day: 12,
    title: "Drive from Dharapani to Kathmandu",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "8–10 hrs" },
    ],
    progressPct: 96,
    description: (
      <>
        <p>
          Drive from Dharapani to Besisahar and continue your journey to Kathmandu. Take the evening to rest or explore the city.
        </p>
      </>
    ),
    altitudes: [
      { label: "Dharapani", value: "1,860 m" },
      { label: "Kathmandu", value: "1,324 m" },
    ],
  },
  {
    day: 13,
    title: "Final Departure from Kathmandu",
    stats: [],
    progressPct: 100,
    description: (
      <>
        <p>
          Our representative will transfer you to the airport for your international departure. Thank you for trekking the Manaslu Circuit with us!
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,324 m" }],
  },
];