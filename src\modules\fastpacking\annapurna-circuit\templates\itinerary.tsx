
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { MapPin, Clock, Mountain, Navigation, Car } from 'lucide-react';
import React from 'react'

const Itinerary = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <MapPin size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-primary">Longer and challenging itinerary</h2>
            </div>

            <Accordion type="multiple" className="space-y-4">
                <AccordionItem value="day-1">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">1</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Drive to Syange
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            {/* Stats */}
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Drive Duration: <strong>6.5 hours total</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Distance: <strong>210 km total</strong></span>
                                </div>
                            </div>

                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '10%' }}></div>
                            </div>

                            <div className="text-dark/85 leading-relaxed space-y-3">
                                <p><strong>Route Options:</strong></p>
                                <ul className="list-disc list-inside space-y-2">
                                    <li><strong>From Pokhara to Besisahar:</strong> 4 hours, covering 105 km</li>
                                    <li><strong>From Kathmandu to Besisahar:</strong> 5 hr 30 min covering 180 km</li>
                                    <li><strong>From Besisahar to Syange:</strong> 1 hour 30 minutes, covering 30 km</li>
                                </ul>
                            </div>

                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Pokhara: 820 m</div>
                                    <div>Kathmandu: 1,400 m</div>
                                    <div>Besisahar: 760 m</div>
                                    <div>Syange: 1,700 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-2">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">2</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Syange to Chame
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>36 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '20%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Syange: 1,700 m</div>
                                    <div>Chame: 2,650 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-3">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">3</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Chame to Ngawal
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>22 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '30%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Chame: 2,650 m</div>
                                    <div>Ngawal: 3,650 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-4">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">4</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Ngawal to Ice Lake and back to Manang
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>26 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '40%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-primary/80">
                                    <div>Ngawal: 3,650 m</div>
                                    <div>Ice Lake: 4,600 m</div>
                                    <div>Manang: 3,519 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-5">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">5</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Acclimatization day at Manang, hike to Chongkar viewpoint
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Mountain size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Activity: <strong>Rest Day & Short Hike</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '50%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Rest Day at Manang and a short hike to Chongkar viewpoint for acclimatization and stunning mountain views.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-6">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">6</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Manang to Tilicho Base Camp
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>11 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>4-5 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '60%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Manang: 3,519 m</div>
                                    <div>Tilicho Base Camp: 4,150 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-7">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">7</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Tilicho Base Camp to Tilicho Lake & run to Yak Kharka
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>20 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '70%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-primary/80">
                                    <div>Tilicho Base Camp: 4,150 m</div>
                                    <div>Tilicho Lake: 4,919 m</div>
                                    <div>Yak Kharka: 4,000 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-8">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">8</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Yak Kharka to Thorung Phedi
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>8 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>2-3 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '75%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Yak Kharka: 4,000 m</div>
                                    <div>Thorung Phedi: 4,540 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-9">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">9</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Thorung Phedi to Muktinath
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>17 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '80%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Hiking during the first half, till Thorung La Pass and running till Muktinath. Cross the famous Thorung La Pass!
                            </p>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-primary/80">
                                    <div>Thorung Phedi: 4,540 m</div>
                                    <div>Thorung La Pass: 5,416 m</div>
                                    <div>Muktinath: 3,710 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-10">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">10</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Muktinath to Marpha via Lubra Village
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>25 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '90%' }}></div>
                            </div>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-primary/80">
                                    <div>Muktinath: 3,710 m</div>
                                    <div>Lubra: 3,050 m</div>
                                    <div>Marpha: 2,650 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-11">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-primary font-medium">Day</span>
                                <span className="text-2xl font-bold text-primary">11</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                   Marpha to Pokhara by Jeep
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Driving Distance: <strong>137 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours drive</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-primary h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                            <p className="text-dark/85">
                                Final day of the adventure! Drive back to Pokhara by jeep, enjoying the scenic mountain roads and reflecting on the incredible fastpacking journey through the Annapurna Circuit.
                            </p>
                            <div className="bg-primary/5 border border-primary rounded-lg p-4">
                                <h4 className="font-semibold text-primary/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-primary/80">
                                    <div>Marpha: 2,650 m</div>
                                    <div>Pokhara: 820 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
}

export default Itinerary