import PackageCard from "@/components/common/cards/card";
import SearchBar from "@/components/search/search-bar";
import { IPackage } from "@/types/package";
import { IApiResponse } from "@/types/response";
import Link from "next/link";

export const revalidate = 0;

function stripHtml(html: string) {
    return html.replace(/<br\s*\/?>/gi, " ").replace(/<[^>]*>/g, "").trim();
}

function truncate(text: string, n = 140) {
    if (text.length <= n) return text;
    return text.slice(0, n).trim() + "…";
}

export default async function SearchPackagesPage(props: {
    searchParams: Promise<{ query?: string; page?: string; field?: string }>;
}) {
    const sp = await props.searchParams;

    const query = (sp.query ?? "").trim();
    const page = Number(sp.page ?? 1);
    const field = (sp.field ?? "name").toLowerCase();
    const limit = 12;

    if (!query) {
        return (
            <div className="container mx-auto px-4 py-12">
                <h1 className="text-2xl md:text-3xl font-semibold mb-2">Search Packages</h1>
                <p className="text-muted-foreground">
                    Type a trekking package name or keyword in the search bar to see results.
                </p>
            </div>
        );
    }

    const url = new URL(`${process.env.NEXT_PUBLIC_API_URL}/package`);
    url.searchParams.set("search", query);
    url.searchParams.set("searchFields", field);
    url.searchParams.set("page", String(page));
    url.searchParams.set("limit", String(limit));

    const res = await fetch(url.toString(), { cache: "no-store" });
    if (!res.ok) {
        return (
            <div className="container mx-auto px-4 py-12">
                <h1 className="text-2xl md:text-3xl font-semibold mb-4">
                    Search Results for “{query}”
                </h1>
                <p className="text-red-600">Failed to load results.</p>
            </div>
        );
    }

    const json = (await res.json()) as IApiResponse<IPackage[]>;
    const packages = json.data ?? [];
    const meta = json.meta;

    return (
        <div className="container mx-auto px-4 py-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                <h1 className="text-2xl md:text-3xl font-semibold">
                    Search Results for “{query}”
                </h1>
                <SearchBar defaultQuery={query} />
            </div>

            {packages.length === 0 ? (
                <p className="text-muted-foreground">No matches found.</p>
            ) : (
                <ul className="grid gap-6 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
                    {packages.map((pkg) => (
                        <li key={pkg.id}>
                            <Link href={`/${pkg.activity?.slug}/${pkg.slug}`} className="block">
                                <PackageCard package={pkg} />
                            </Link>
                        </li>
                    ))}
                </ul>
            )}

            {meta && meta.lastPage > 1 && (
                <div className="mt-8 flex items-center justify-center gap-3">
                    <PaginationLink
                        disabled={page <= 1}
                        href={`/search-packages?query=${encodeURIComponent(query)}&page=${page - 1
                            }`}
                    >
                        Previous
                    </PaginationLink>
                    <span className="text-sm text-muted-foreground">
                        Page {meta.currentPage} of {meta.lastPage}
                    </span>
                    <PaginationLink
                        disabled={page >= meta.lastPage}
                        href={`/search-packages?query=${encodeURIComponent(query)}&page=${page + 1
                            }`}
                    >
                        Next
                    </PaginationLink>
                </div>
            )}
        </div>
    );
}

function PaginationLink({
    href,
    children,
    disabled,
}: {
    href: string;
    children: React.ReactNode;
    disabled?: boolean;
}) {
    if (disabled)
        return (
            <span className="px-4 py-2 rounded-full border text-sm text-muted-foreground opacity-60 cursor-not-allowed">
                {children}
            </span>
        );
    return (
        <Link
            href={href}
            className="px-4 py-2 rounded-full border text-sm hover:bg-gray-50"
        >
            {children}
        </Link>
    );
}
