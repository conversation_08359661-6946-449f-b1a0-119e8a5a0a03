import { useQuery } from '@tanstack/react-query';
import { IPackage } from '@/types/package';
import { IApiResponse } from '@/types/response';

const fetchPackages = async (): Promise<IApiResponse<IPackage[]>> => {
  const response = await fetch(
    'https://api.trailandtreknepal.com/package?sort=createdAt%3Adesc'
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error('Failed to load packages');
  }

  return data;
};

export const usePackages = () => {
  return useQuery<IApiResponse<IPackage[]>, Error>({
    queryKey: ['packages'],
    queryFn: fetchPackages,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

// Usage example:
// const { data: packages, isLoading: loading, error } = usePackages();
