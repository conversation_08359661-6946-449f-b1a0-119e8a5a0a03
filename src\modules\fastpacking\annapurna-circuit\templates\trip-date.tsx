// app/components/TripDateSelector.tsx
"use client"

import { useState } from "react"
import { Calendar, Info, MapPin } from "lucide-react"
import { DateRange, DayPicker } from "react-day-picker"
import "react-day-picker/dist/style.css"
import { addDays } from "date-fns"


export default function TripDateSelector() {
    const [range, setRange] = useState<DateRange | undefined>(undefined)

    const handleDaySelect = (day: Date | undefined) => {
        if (!day) return
        const end = addDays(day, 10)
        setRange({ from: day, to: end })
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <Calendar size={16} className="text-light" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-brand">Choose your Trip Dates</h2>
            </div>

            <div className="bg-brand/10 p-4 rounded-lg mb-6 flex flex-col gap-3">
                <div className="flex gap-1">
                    <Info className="w-6 h-6 text-brand " />
                    <p className="text-gray-700 text-base font-bold">
                        Note to Travelers:
                    </p>
                </div>
                <p className="text-gray-700">
                    We organize exclusive private treks for groups
                    of two or more, ensuring a comfortable, personal, and intimate trek experience
                    with no outside participants.
                </p>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6 text-gray-700">
                <div className="flex items-center gap-2">
                    <Calendar className="w-7 h-7 text-brand" />
                    <div>
                        <div className="text-sm">Trip Duration</div>
                        <div className="font-medium">11 Days</div>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <MapPin className="w-7 h-7 text-brand" />
                    <div>
                        <div className="text-sm">Trip Start/End Point</div>
                        <div className="font-medium">Kathmandu / Pokhara</div>
                    </div>
                </div>
            </div>

            <div className="bg-secondary/10 p-4 rounded-lg mb-6 flex flex-col gap-3">
                <div className="flex gap-1">
                    <Calendar className="w-5 h-5 text-brand" />
                    <p className="text-gray-700 text-base font-bold">
                        Your Trek, Your Timeline:
                    </p>
                </div>
                <p className="text-gray-700">
                    Since we organize private treks, feel free
                    to choose your date. Make sure your travel plans, including arrival and departure
                    dates in Kathmandu, match your itinerary.
                </p>
            </div>

            <DayPicker
                mode="single"
                selected={range?.from}
                onSelect={handleDaySelect}
                numberOfMonths={2}
                pagedNavigation
                disabled={{ before: new Date() }}
                className="bg-white rounded-lg shadow p-4 w-full"
                modifiers={{
                    selectedRange: range ? { from: range.from, to: range.to } : undefined
                }}
                modifiersClassNames={{
                    selectedRange: "bg-brand text-white rounded-lg"
                }}
            />

            <button className="mt-6 w-full md:w-auto px-6 py-3 bg-brand text-white rounded-lg font-medium">
                Continue Booking
            </button>
        </div>
    )
}
