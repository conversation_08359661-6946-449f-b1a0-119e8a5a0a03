"use client"

import { DollarSign } from "lucide-react"

export default function GroupSizeDiscounts() {
    const discounts = [
        { group: "2 – 5 Pax", price: "US$ 720" },
        { group: "6 – 9 Pax", price: "US$ 690" },
        { group: "10 – 17 Pax", price: "US$ 650" },
        { group: "18 – 25 Pax", price: "US$ 600" },
    ]

    return (
        <div className="container mx-auto my-12 px-4">
            {/* Header */}
            <div className="flex items-center gap-2 mb-4">
                <DollarSign className="w-6 h-6 text-brand" />
                <h2 className="text-2xl font-bold text-brand">Group‐Size Discounts</h2>
            </div>

            {/* Table */}
            <table className="w-full text-gray-700">
                <thead>
                    <tr>
                        <th className="text-left pb-2 border-b border-gray-300">No. of Persons</th>
                        <th className="text-right pb-2 border-b border-gray-300">Price per Person</th>
                    </tr>
                </thead>
                <tbody>
                    {discounts.map((d, i) => (
                        <tr
                            key={i}
                            className=" "
                        >
                            <td colSpan={2} className="py-2">
                                <div className="flex items-center">
                                    <span className="font-medium">{d.group}</span>
                                    <span className="flex-grow border-t border-dashed border-gray-500 mx-4" />
                                    <span className="font-medium">{d.price}</span>
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    )
}
