// components/TrekInclusionsExclusions.tsx
// components/TrekInclusionsExclusions.tsx
'use client';

import React, { JSX } from 'react';
import { Check, X } from 'lucide-react';
import HtmlContentDisplay from '@/components/html-content-display';

export interface Category {
  title: string;
  icon: JSX.Element;
  items: string[];
}

export interface TrekInclusionsExclusionsProps {
  inclusions: Category[];
  exclusions: Category[];
}

export default function TrekInclusionsExclusions({
  inclusions,
  exclusions,
}: TrekInclusionsExclusionsProps) {
  return (
    <div className="container mx-auto px-2 md:px-4 space-y-8">
      {/* INCLUDED */}
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
            <Check size={16} className="text-light" />
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-brand">
            What&apos;s Included
          </h2>
        </div>
        <div className="space-y-6">
          {inclusions.map((cat) => (
            <div
              key={cat.title}
              className="border border-blue-200 rounded-lg p-4 bg-gray-50"
            >
              <div className="flex items-center gap-2 mb-4">
                {cat.icon}
                <h3 className="text-lg font-semibold">{cat.title}</h3>
              </div>
              <ul className="space-y-2 pl-2">
                {cat.items.map((item, i) => (
                  <li key={i} className="flex items-start gap-2">
                    {/* <Check className="w-4 h-4 text-dark mt-1 flex-shrink-0" /> */}
                    <HtmlContentDisplay
                      htmlContent={item}
                      className="text-gray-700 leading-relaxed"
                    />
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      <hr className="w-full h-px bg-gray-200 border-0 mb-4" />

      {/* EXCLUDED */}
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
            <X size={16} className="text-light" />
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-brand">
            What&apos;s Excluded
          </h2>
        </div>
        <div className="space-y-6">
          {exclusions.map((cat) => (
            <div
              key={cat.title}
              className="border border-blue-200 rounded-lg p-4 bg-white"
            >
              <div className="flex items-center gap-2 mb-4">
                {cat.icon}
                <h3 className="text-lg font-semibold">{cat.title}</h3>
              </div>
              <ul className="space-y-2 pl-2">
                {cat.items.map((item, i) => (
                  <li key={i} className="flex items-start gap-2">
                    {/* <X className="w-4 h-4 text-dark mt-1 flex-shrink-0" /> */}
                    <HtmlContentDisplay
                      htmlContent={item}
                      className="text-gray-700 leading-relaxed"
                    />
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// // components/TrekInclusionsExclusions.tsx
// "use client"

// import React, { JSX } from "react"
// import {
//   Car,
//   Bed,
//   Coffee,
//   User,
//   FileText,
//   Check,
//   X,
//   PlaneIcon,
// } from "lucide-react"

// interface Category {
//   title: string
//   icon: JSX.Element
//   items: string[]
// }

// const inclusions: Category[] = [
//   {
//     title: "Transportation",
//     icon: <Car className="w-5 h-5 text-dark" />,
//     items: [
//       "Kathmandu ↔ Pokhara ↔ Kathmandu on tourist bus",
//       "Private airport transfers in Kathmandu",
//     ],
//   },
//   {
//     title: "Accommodations",
//     icon: <Bed className="w-5 h-5 text-dark" />,
//     items: [
//       "Tea-house lodges every night of the trek",
//       "2 nights hotel in Pokhara (B&B)",
//     ],
//   },
//   {
//     title: "Food",
//     icon: <Coffee className="w-5 h-5 text-dark" />,
//     items: [
//       "3 meals per day on trek (B, L, D)",
//       "Tea & coffee twice daily",
//       "Fresh fruit each evening",
//     ],
//   },
//   {
//     title: "Guide & Porter",
//     icon: <User className="w-5 h-5 text-dark" />,
//     items: [
//       "Licensed English-speaking trekking guide",
//       "Porter to carry up to 15 kg per two trekkers",
//     ],
//   },
//   {
//     title: "Visa & Permits",
//     icon: <FileText className="w-5 h-5 text-dark" />,
//     items: [
//       "All required trek permits & TIMS card",
//       "Government taxes and entry fees",
//     ],
//   },
// ]

// const exclusions: Category[] = [
//   {
//     title: "International Flight",
//     icon: <PlaneIcon className="w-5 h-5 text-dark" />,
//     items: ["International airfare & Nepal visa fee"],
//   },
//   {
//     title: "Kathmandu Hotel",
//     icon: <Bed className="w-5 h-5 text-dark" />,
//     items: [
//       "Accommodation in Kathmandu before/after trek",
//       "Extra-night hotel stays due to early/late flights",
//     ],
//   },
//   {
//     title: "Food in Kathmandu",
//     icon: <Coffee className="w-5 h-5 text-dark" />,
//     items: [
//       "All meals in Kathmandu",
//       "Lunch & dinner in Pokhara",
//     ],
//   },
//   {
//     title: "Guide & Porter Tips",
//     icon: <User className="w-5 h-5 text-dark" />,
//     items: ["Tips for guides and porters"],
//   },
//   {
//     title: "Travel Insurance",
//     icon: <FileText className="w-5 h-5 text-dark" />,
//     items: ["Personal travel insurance (mandatory)"],
//   },
// ]

// export default function TrekInclusionsExclusions() {
//   return (
//     <div className="container mx-auto px-2 md:px-4 space-y-8">
//       {/* INCLUDED */}
//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <Check size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">What&apos;s Included</h2>
//         </div>

//         <div className="space-y-6">
//           {inclusions.map((cat) => (
//             <div
//               key={cat.title}
//               className="border border-blue-200 rounded-lg p-4 bg-gray-50"
//             >
//               <div className="flex items-center gap-2 mb-4">
//                 {cat.icon}
//                 <h3 className="text-lg font-semibold">{cat.title}</h3>
//               </div>
//               <ul className="space-y-2 pl-2">
//                 {cat.items.map((item, i) => (
//                   <li key={i} className="flex items-start gap-2">
//                     <Check className="w-4 h-4 text-dark mt-1 flex-shrink-0" />
//                     <span className="text-gray-700">{item}</span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           ))}
//         </div>
//       </div>

//       <hr className="w-full h-px bg-gray-200 border-0 mb-4" />
//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <X size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">What&apos;s Excluded</h2>
//         </div>
//         <div className="space-y-6">
//           {exclusions.map((cat) => (
//             <div
//               key={cat.title}
//               className="border border-blue-200 rounded-lg p-4 bg-white"
//             >
//               <div className="flex items-center gap-2 mb-4">
//                 {cat.icon}
//                 <h3 className="text-lg font-semibold">{cat.title}</h3>
//               </div>
//               <ul className="space-y-2 pl-2">
//                 {cat.items.map((item, i) => (
//                   <li key={i} className="flex items-start gap-2">
//                     <X className="w-4 h-4 text-dark mt-1 flex-shrink-0" />
//                     <span className="text-gray-700">{item}</span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   )
// }
// 'use client';

// import React, { JSX } from 'react';
// import { Check, X } from 'lucide-react';
// import { IPackageInclusions } from '@/types/package';

// export interface Category {
//   title: string;
//   icon: JSX.Element;
//   items: string[];
// }

// export interface TrekInclusionsExclusionsProps {
//   inclusions?: IPackageInclusions;
//   exclusions?: IPackageInclusions;
// }

// export default function TrekInclusionsExclusions({
//   inclusions,
//   exclusions,
// }: TrekInclusionsExclusionsProps) {
//   return (
//     <div className="container mx-auto px-2 md:px-4 space-y-8">

//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <Check size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">
//             {inclusions?.title}
//           </h2>
//         </div>
//         <div
//           className="space-y-6"
//           dangerouslySetInnerHTML={{ __html: inclusions?.details || '' }}
//         ></div>
//       </div>

//       <hr className="w-full h-px bg-gray-200 border-0 mb-4" />

//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <X size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">
//             {exclusions?.title}
//           </h2>
//         </div>
//         <div
//           className="space-y-6"
//           dangerouslySetInnerHTML={{ __html: exclusions?.details || '' }}
//         ></div>
//       </div>
//     </div>
//   );
// }

// // components/TrekInclusionsExclusions.tsx
// "use client"

// import React, { JSX } from "react"
// import {
//   Car,
//   Bed,
//   Coffee,
//   User,
//   FileText,
//   Check,
//   X,
//   PlaneIcon,
// } from "lucide-react"

// interface Category {
//   title: string
//   icon: JSX.Element
//   items: string[]
// }

// const inclusions: Category[] = [
//   {
//     title: "Transportation",
//     icon: <Car className="w-5 h-5 text-dark" />,
//     items: [
//       "Kathmandu ↔ Pokhara ↔ Kathmandu on tourist bus",
//       "Private airport transfers in Kathmandu",
//     ],
//   },
//   {
//     title: "Accommodations",
//     icon: <Bed className="w-5 h-5 text-dark" />,
//     items: [
//       "Tea-house lodges every night of the trek",
//       "2 nights hotel in Pokhara (B&B)",
//     ],
//   },
//   {
//     title: "Food",
//     icon: <Coffee className="w-5 h-5 text-dark" />,
//     items: [
//       "3 meals per day on trek (B, L, D)",
//       "Tea & coffee twice daily",
//       "Fresh fruit each evening",
//     ],
//   },
//   {
//     title: "Guide & Porter",
//     icon: <User className="w-5 h-5 text-dark" />,
//     items: [
//       "Licensed English-speaking trekking guide",
//       "Porter to carry up to 15 kg per two trekkers",
//     ],
//   },
//   {
//     title: "Visa & Permits",
//     icon: <FileText className="w-5 h-5 text-dark" />,
//     items: [
//       "All required trek permits & TIMS card",
//       "Government taxes and entry fees",
//     ],
//   },
// ]

// const exclusions: Category[] = [
//   {
//     title: "International Flight",
//     icon: <PlaneIcon className="w-5 h-5 text-dark" />,
//     items: ["International airfare & Nepal visa fee"],
//   },
//   {
//     title: "Kathmandu Hotel",
//     icon: <Bed className="w-5 h-5 text-dark" />,
//     items: [
//       "Accommodation in Kathmandu before/after trek",
//       "Extra-night hotel stays due to early/late flights",
//     ],
//   },
//   {
//     title: "Food in Kathmandu",
//     icon: <Coffee className="w-5 h-5 text-dark" />,
//     items: [
//       "All meals in Kathmandu",
//       "Lunch & dinner in Pokhara",
//     ],
//   },
//   {
//     title: "Guide & Porter Tips",
//     icon: <User className="w-5 h-5 text-dark" />,
//     items: ["Tips for guides and porters"],
//   },
//   {
//     title: "Travel Insurance",
//     icon: <FileText className="w-5 h-5 text-dark" />,
//     items: ["Personal travel insurance (mandatory)"],
//   },
// ]

// export default function TrekInclusionsExclusions() {
//   return (
//     <div className="container mx-auto px-2 md:px-4 space-y-8">
//       {/* INCLUDED */}
//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <Check size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">What&apos;s Included</h2>
//         </div>

//         <div className="space-y-6">
//           {inclusions.map((cat) => (
//             <div
//               key={cat.title}
//               className="border border-blue-200 rounded-lg p-4 bg-gray-50"
//             >
//               <div className="flex items-center gap-2 mb-4">
//                 {cat.icon}
//                 <h3 className="text-lg font-semibold">{cat.title}</h3>
//               </div>
//               <ul className="space-y-2 pl-2">
//                 {cat.items.map((item, i) => (
//                   <li key={i} className="flex items-start gap-2">
//                     <Check className="w-4 h-4 text-dark mt-1 flex-shrink-0" />
//                     <span className="text-gray-700">{item}</span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           ))}
//         </div>
//       </div>

//       <hr className="w-full h-px bg-gray-200 border-0 mb-4" />
//       <div>
//         <div className="flex items-center gap-3 mb-6">
//           <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//             <X size={16} className="text-light" />
//           </div>
//           <h2 className="text-2xl md:text-3xl font-bold text-brand">What&apos;s Excluded</h2>
//         </div>
//         <div className="space-y-6">
//           {exclusions.map((cat) => (
//             <div
//               key={cat.title}
//               className="border border-blue-200 rounded-lg p-4 bg-white"
//             >
//               <div className="flex items-center gap-2 mb-4">
//                 {cat.icon}
//                 <h3 className="text-lg font-semibold">{cat.title}</h3>
//               </div>
//               <ul className="space-y-2 pl-2">
//                 {cat.items.map((item, i) => (
//                   <li key={i} className="flex items-start gap-2">
//                     <X className="w-4 h-4 text-dark mt-1 flex-shrink-0" />
//                     <span className="text-gray-700">{item}</span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   )
// }
