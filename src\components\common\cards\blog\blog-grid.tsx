'use client'

import React from 'react';
import BlogCard from './blog-card';
import { IBlog } from '@/types/blogs';

export default function BlogGrid({ initialPosts }: { initialPosts: IBlog[] }) {
  const posts = initialPosts ?? [];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Latest Trekking Blogs
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover inspiring stories, expert tips, and comprehensive guides for your next Himalayan adventure
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((blog) => (
            <BlogCard
              key={blog.id}
              blog={blog}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

