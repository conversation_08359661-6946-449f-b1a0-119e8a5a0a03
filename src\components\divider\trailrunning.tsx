import Image from 'next/image'
import React from 'react'

const TrailRunningDivider = () => {
   return (
    <div className="absolute bottom-12 sm:bottom-0 md:bottom-0 left-1/2 -translate-x-1/2 z-20 w-full flex justify-center">
      <div className="w-full max-w-5xl md:max-w-6xl lg:max-w-full overflow-hidden">
        <Image
          src="/images/icons/running.png"
          alt="TrailRunning"
          width={2200}   
          height={1000} 
          priority
          className="object-cover w-full"
        />
      </div>
    </div>
  )
}

export default TrailRunningDivider



// import Image from 'next/image'
// import React from 'react'

// const TrailRunningDivider = () => {
//     return (
//         <div className=" w-full overflow-hidden">
//             <Image
//                 src="/images/icon/running.svg"
//                 alt="Trail Running Icon"
//                 width={1920}
//                 height={100}
//                 priority
//                 className="object-cover"
//             />
//         </div>
//     )
// }

// export default TrailRunningDivider