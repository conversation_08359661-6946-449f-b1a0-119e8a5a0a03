'use client'

import React, { useState } from 'react'
import { ChevronDown, Star, CheckCircle, User, MessageCircle, Clock } from 'lucide-react'

interface PricingCardProps {
  originalPrice?: string
  currentPrice?: string
  currency?: string
  discount?: string
  rating?: number
  reviewCount?: number
  features?: string[]
  whatsappNumber?: string
  onPlanTrip?: () => void
  onAskQuestion?: () => void
}

const PricingCard: React.FC<PricingCardProps> = ({
  originalPrice = "USD $1136",
  currentPrice = "USD $986",
  currency = "USD ($)",
  discount = "13% off",
  rating = 5,
  reviewCount = 6,
  features = ["Satisfied Client", "Personalised Guide", "Instant Response"],
  whatsappNumber = "+977-9851402018",
  onPlanTrip = () => {},
  onAskQuestion = () => {}
}) => {
  const [selectedCurrency, setSelectedCurrency] = useState(currency)

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={index < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
      />
    ))
  }

  const getFeatureIcon = (feature: string) => {
    switch (feature.toLowerCase()) {
      case 'satisfied client':
        return <User size={16} className="text-gray-600" />
      case 'personalised guide':
        return <CheckCircle size={16} className="text-gray-600" />
      case 'instant response':
        return <Clock size={16} className="text-gray-600" />
      default:
        return <CheckCircle size={16} className="text-gray-600" />
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm mx-auto">
      {/* Header Badges */}
      <div className="flex justify-between items-start mb-6">
        <span className="bg-secondary text-white px-3 py-1 rounded-md text-sm font-medium">
          Best Seller
        </span>
        <span className="bg-red text-white px-3 py-1 rounded-md text-sm font-medium">
          {discount}
        </span>
      </div>

      {/* Currency Selector */}
      <div className="mb-6">
        <div className="relative">
          <select 
            value={selectedCurrency}
            onChange={(e) => setSelectedCurrency(e.target.value)}
            className="w-full appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-gray-700 focus:outline-none focus:ring-2 focus:ring-brand"
          >
            <option value="USD ($)">USD ($)</option>
            <option value="EUR (€)">EUR (€)</option>
            <option value="GBP (£)">GBP (£)</option>
          </select>
          <ChevronDown size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* Price Section */}
      <div className="mb-6">
        <p className="text-gray-600 text-sm mb-2">Price Per person</p>
        <div className="flex items-baseline gap-3">
          <span className="text-4xl font-bold text-gray-900">{currentPrice}</span>
          <span className="text-lg text-gray-500 line-through">{originalPrice}</span>
        </div>
      </div>

      {/* Rating */}
      <div className="flex items-center gap-2 mb-6">
        <div className="flex items-center">
          {renderStars()}
        </div>
        <span className="text-blue-500 text-sm font-medium">{reviewCount} reviews</span>
      </div>

      {/* Features */}
      <div className="space-y-3 mb-6">
        {features.map((feature, index) => (
          <div key={index} className="flex items-center gap-3">
            {getFeatureIcon(feature)}
            <span className="text-gray-700 text-sm">{feature}</span>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={onPlanTrip}
          className="w-full bg-brand hover:bg-brand/80 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200"
        >
          Plan a trip
        </button>
        
        <button
          onClick={onAskQuestion}
          className="w-full bg-secondary hover:bg-secondary/80 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200"
        >
          Ask a Question
        </button>
        
        <div className="border-2 border-green-500 rounded-md p-3">
          <div className="flex items-center gap-2 text-green-600">
            <MessageCircle size={16} />
            <span className="text-sm font-medium">Get Instant Response:</span>
          </div>
          <p className="text-green-600 text-sm mt-1">
            {whatsappNumber} (WhatsApp)
          </p>
        </div>
      </div>
    </div>
  )
}

export default PricingCard