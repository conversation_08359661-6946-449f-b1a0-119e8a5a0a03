"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Star } from "lucide-react"
import Image from "next/image"

// Put your image paths here (public/ folder paths).
const IMAGES = [
    "/images/fastpacking/hero/image1.webp",
    "/images/fastpacking/hero/image2.webp",
    "/images/fastpacking/hero/image3.webp",
    "/images/fastpacking/hero/image4.webp",
    "/images/fastpacking/hero/image5.webp",
    "/images/fastpacking/hero/image6.webp",
    "/images/fastpacking/hero/image7.webp",
    "/images/fastpacking/hero/image1.webp",
    "/images/fastpacking/hero/image2.webp",
    "/images/fastpacking/hero/image3.webp",
    "/images/fastpacking/hero/image4.webp",

]

export default function WhatsItLike() {
    const rating = 5.0
    const reviewsCount = 15
    const heading = "WHAT'S IT LIKE?"

    const [perPage, setPerPage] = useState(6)
    const [page, setPage] = useState(0)

    // Responsive per-page counts
    useEffect(() => {
        const calc = () => {
            const w = window.innerWidth
            if (w < 640) setPerPage(2)
            else if (w < 1024) setPerPage(4)
            else setPerPage(6)
            setPage(0)
        }
        calc()
        window.addEventListener("resize", calc)
        return () => window.removeEventListener("resize", calc)
    }, [])

    const totalPages = Math.ceil(IMAGES.length / perPage)
    const goTo = (p: number) =>
        setPage(Math.min(Math.max(p, 0), totalPages - 1))

    return (
        <section className="bg-dark text-white">
            <div className="container mx-auto px-4 py-10">
                {/* Header */}
                <div className="mb-6">
                    <h2 className="text-2xl md:text-3xl font-extrabold tracking-tight mb-3">
                        {heading}
                    </h2>
                    <div className="flex items-center gap-2 text-sm">
                        <div className="flex text-brand">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Star key={i} className="h-4 w-4 fill-current" />
                            ))}
                        </div>
                        <span className="font-semibold">{rating.toFixed(1)}</span>
                        <span className="text-neutral-400">|</span>
                        <button
                            type="button"
                            className="hover:underline"
                            onClick={() => {
                                const el = document.getElementById("reviews")
                                if (el) el.scrollIntoView({ behavior: "smooth" })
                            }}
                        >
                            {reviewsCount} reviews
                        </button>
                    </div>
                </div>

                {/* Slider */}
                <div className="relative">
                    <div className="overflow-hidden">
                        <div
                            className="flex transition-transform duration-500 ease-out"
                            style={{
                                width: `${100 * totalPages}%`,
                                transform: `translateX(-${(100 / totalPages) * page}%)`,
                            }}
                        >
                            {IMAGES.map((src, i) => (
                                <div
                                    key={src + i}
                                    className="p-1"
                                    style={{
                                        width: `${100 / (perPage * totalPages)}%`,
                                    }}
                                >
                                    <div className="relative w-full h-48 md:h-56 rounded-md overflow-hidden bg-neutral-800">
                                        <Image
                                            src={src}
                                            alt={`Gallery ${i + 1}`}
                                            fill
                                            className="object-cover"
                                            sizes="(max-width:640px) 50vw, (max-width:1024px) 25vw, 16vw"
                                            priority={i < perPage}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {page > 0 && (
                        <button
                            onClick={() => goTo(page - 1)}
                            aria-label="Previous"
                            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2
                         bg-black/50 hover:bg-black/70 p-2 rounded-full transition"
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </button>
                    )}

                    {page < totalPages - 1 && (
                        <button
                            onClick={() => goTo(page + 1)}
                            aria-label="Next"
                            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2
                         bg-black/50 hover:bg-black/70 p-2 rounded-full transition"
                        >
                            <ChevronRight className="h-5 w-5" />
                        </button>
                    )}
                </div>

                {totalPages > 1 && (
                    <div className="mt-4 flex justify-center gap-2">
                        {Array.from({ length: totalPages }).map((_, i) => (
                            <button
                                key={i}
                                onClick={() => goTo(i)}
                                aria-label={`Go to page ${i + 1}`}
                                className={`h-2.5 w-2.5 rounded-full transition ${i === page
                                        ? "bg-white scale-110"
                                        : "bg-neutral-600 hover:bg-neutral-400"
                                    }`}
                            />
                        ))}
                    </div>
                )}
            </div>
            <div className="h-px w-full bg-neutral-800" />
        </section>
    )
}
