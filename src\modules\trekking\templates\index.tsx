'use client';

import PricingCard from '@/components/common/sidebar/pricingcard';
import TrekInfo from '@/components/common/trek-overview/trek';
import Gears from '@/modules/fastpacking/components/gears';
import Highlight, {
  HighlightItem,
} from '@/modules/fastpacking/components/highlight';
import Mapandchart from '@/modules/fastpacking/components/map';
import Overview from '@/modules/fastpacking/components/overview';
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section';
import HeroSection from '@/modules/fastpacking/components/hero';
import TrekInclusionsExclusions, {
  Category,
} from '@/modules/fastpacking/components/included-excluded';
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed';
import ItinerarySummary, {
  DaySummary,
} from '@/modules/fastpacking/components/itinerary-summary';
import StackedReviews, {
  Review,
} from '@/modules/fastpacking/components/review';
import TravelersReview, {
  ReviewVideo,
} from '@/modules/fastpacking/components/review-video';
import { useRouter, useParams } from 'next/navigation';
import React, { useMemo } from 'react';
import {
  Ban,
  Car,
  Clock,
  FileText,
  Mountain,
  Navigation,
  ShieldCheck,
} from 'lucide-react';
import { ScrollSpyTabs } from '@/components/common/sticky-scroll/sticky-scroll';
import { IPackage } from '@/types/package';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import HtmlContentDisplay from '@/components/html-content-display';

const TrekDetailPage = ({ data }: { data: IPackage }) => {
  const params = useParams();
  const router = useRouter();
  const slug = params.slug as string;

  const handleInquireNow = () => {
    router.push('/contact');
  };

  const handleDatesPrice = () => {
    router.push('/dates-price');
  };

  const handleBookNow = () => {
    if (data.bookingLink) {
      window.open(data.bookingLink, '_blank');
    } else {
      router.push('/booking');
    }
  };

  const sectionIds = [
    { id: 'trek-info', label: 'Trek Info' },
    { id: 'highlights', label: 'Highlights' },
    { id: 'overview', label: 'Overview' },
    { id: 'gallery', label: 'Gallery' },
    { id: 'itinerary', label: 'Itinerary' },
    { id: 'map', label: 'Map' },
    { id: 'inclusions', label: 'Inclusions/Exclusions' },
    { id: 'gears', label: 'Gears' },
    { id: 'package-info', label: 'Package Information' },
    { id: 'reviews', label: 'Reviews' },
  ];

  const faqSchema = useMemo(() => {
    const faqsForSchema = (data.faq || [])
      .filter((faq) => faq.published)
      .map((faq) => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer.replace(/<[^>]*>?/gm, '').trim(),
        },
      }));

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqsForSchema.length > 0 ? faqsForSchema : [],
    };
  }, [data.faq]);

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg">Trek not found</p>
          <button
            onClick={() => router.push('/trekking')}
            className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Treks
          </button>
        </div>
      </div>
    );
  }

  const customTrekData = {
    destination: `${data.region?.name}`,
    accommodation: data.accomodation,
    duration: data.duration,
    maxElevation: `${data.altitude}`,
    group: data.groupSize,
    region: data.region?.name,
    type: data.type,
    bestSeason: data.bestSeason,
    grade: data.grade,
  };

  const heroImage = data.mainImage || data.thumbnail;
  const heroTitle = data.name;

  const highlights: HighlightItem[] = data.highlights?.description?.trim()
    ? data.highlights.description
        .split('\n')
        .filter((line) => line.trim())
        .map((text) => ({ text: text.trim() }))
    : [];

  const hasDescriptionContent = data.description?.description?.trim();
  const content = {
    paragraphs: hasDescriptionContent
      ? [data.description?.description.replace(/<[^>]*>/g, '').trim()]
      : [],
  };

  const shortItinerary: DaySummary[] = data.shortItinerary?.points?.length
    ? data.shortItinerary.points.map((point, index) => ({
        day: index + 1,
        title: point,
      }))
    : [];

  const galleryItems = data.gallery?.PackageGalleryImage?.length
    ? data.gallery.PackageGalleryImage.filter(
        (item) => item.src && item.src.trim() !== ''
      ).map((item, index) => ({
        src: item.src,
        alt: item.caption || `${data.name} - Image ${index + 1}`,
      }))
    : [];

  const detailedItinerary = data.itinerary?.length
    ? data.itinerary
        .sort((a, b) => a.day - b.day)
        .map((item, index, array) => {
          const stats = [];
          if (item.trekDistance && item.trekDistance !== 'n/a') {
            stats.push({
              icon: <Navigation size={16} className="text-dark/80" />,
              label: 'Distance',
              value: item.trekDistance,
            });
          }

          if (item.trekDuration && item.trekDuration !== 'n/a') {
            stats.push({
              icon: <Clock size={16} className="text-dark/80" />,
              label: 'Duration',
              value: item.trekDuration,
            });
          }

          if (item.drivingHours && item.drivingHours !== 'n/a') {
            stats.push({
              icon: <Car size={16} className="text-dark/80" />,
              label: 'Drive Duration',
              value: `${item.drivingHours}`,
            });
          }

          if (item.highestAltitude && item.highestAltitude !== 'n/a') {
            stats.push({
              icon: <Mountain size={16} className="text-dark/80" />,
              label: 'Altitude',
              value: `${item.highestAltitude}`,
            });
          }

          return {
            day: item.day,
            title: item.title,
            stats: stats,
            progressPct: Math.round(((index + 1) / array.length) * 100),
            description: item.activity,
            altitudes: item.highestAltitude
              ? [
                  {
                    label: item.title,
                    value: `${item.highestAltitude}`,
                  },
                ]
              : undefined,
            image: item.image,
          };
        })
    : [];

  const apiReviews: Review[] = data.review?.length
    ? data.review
        .filter((review) => review.published && review.rating && review.comment)
        .map((review, index) => ({
          id: index + 1,
          name: review.name,
          role: 'Trekker',
          company: 'Adventure Enthusiast',
          rating: review.rating,
          content: review.comment,
          avatar: review.reviewImage,
        }))
    : [];

  const apiVideos: ReviewVideo[] = data.ytVideo?.links?.length
    ? data.ytVideo.links.map((link, index) => {
        const youtubeId = link.includes('youtube.com/watch?v=')
          ? link.split('v=')[1]?.split('&')[0]
          : link.includes('youtu.be/')
          ? link.split('youtu.be/')[1]?.split('?')[0]
          : link;
        return {
          id: `${index + 1}`,
          title: data.ytVideo?.title || `${data.name} - Video ${index + 1}`,
          youtubeId: youtubeId,
        };
      })
    : [];

  const apiInclusions: Category[] = data.inclusions?.details?.trim()
    ? [
        {
          title: data.inclusions.title || "What's Included",
          icon: <ShieldCheck className="w-5 h-5 text-dark" />,
          items: data.inclusions.details
            .split('\n')
            .filter((item) => item.trim())
            .map((item) => item.trim()),
        },
      ]
    : [];

  const apiExclusions: Category[] = data.exclusions?.details?.trim()
    ? [
        {
          title: data.exclusions.title || "What's Not Included",
          icon: <Ban className="w-5 h-5 text-dark" />,
          items: data.exclusions.details
            .split('\n')
            .filter((item) => item.trim())
            .map((item) => item.trim()),
        },
      ]
    : [];

  return (
    <>
      {heroImage && (
        <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
          <HeroSection
            imageSrc={heroImage}
            imageAlt={
              data.mainImageAlt ||
              `${heroTitle} - ${data.type} in ${data.region?.name}`
            }
            title={heroTitle}
          />
        </div>
      )}

      <div className="container mx-auto px-2 md:px-4 py-8">
        <ScrollSpyTabs sectionIds={sectionIds} />

        <div className="flex flex-col lg:flex-row gap-8">
          <div className="order-1 lg:order-2 lg:w-80 lg:max-w-80 lg:flex-shrink-0 xl:w-96 xl:max-w-96">
            <div className="sticky top-54 space-y-6">
              <PricingCard
                duration={data.duration}
                originalPrice={data.discountPrice ? `${data.price}` : undefined}
                currentPrice={`${data.discountPrice || data.price} pp`}
                onInquireNow={handleInquireNow}
                onDatesPrice={handleDatesPrice}
                onBookNow={handleBookNow}
              />

              {data.groupPrice?.filter((gp) => gp.published).length > 0 && (
                <div className="bg-white p-4 rounded-lg border">
                  <h3 className="font-semibold mb-3">Group Pricing</h3>
                  <div className="space-y-2">
                    {data.groupPrice
                      .filter((gp) => gp.published)
                      .map((gp) => (
                        <div
                          key={gp.id}
                          className="flex justify-between text-sm"
                        >
                          <span>{gp.numberOfPeople} people:</span>
                          <span className="font-medium">
                            {gp.pricePerPerson}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {data.pdfBrochure && (
                <div className="bg-white p-4 rounded-lg border">
                  <h3 className="font-semibold mb-3">Download Brochure</h3>
                  <a
                    href={data.pdfBrochure}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm"
                  >
                    <FileText className="w-4 h-4" />
                    Download PDF Brochure
                  </a>
                </div>
              )}
            </div>
          </div>

          <div className="order-2 lg:order-1 flex-1 lg:min-w-0">
            <section id="trek-info" className="scroll-mt-[200px]">
              <TrekInfo trekData={customTrekData} />
              <hr className="w-full h-px bg-gray-200 border-0 my-2" />

              {data.overviewDescription?.trim() && (
                <div className="prose prose-lg max-w-none">
                  <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                    <HtmlContentDisplay
                      htmlContent={data.overviewDescription}
                      className="text-justify"
                    />
                  </div>
                </div>
              )}
            </section>

            {highlights.length > 0 && (
              <section id="highlights" className="scroll-mt-[200px]">
                <Highlight items={highlights} />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            {hasDescriptionContent && (
              <section id="overview" className="scroll-mt-[200px]">
                <Overview
                  content={{
                    htmlContent: data.description?.description || '',
                  }}
                  note={{
                    paragraphs: [
                      `This ${data.type.toLowerCase()} has a ${data.grade.toLowerCase()} difficulty rating. Proper preparation and fitness are essential.`,
                      `Best season for this adventure is ${data.bestSeason}. Weather conditions can change rapidly in the mountains.`,
                    ],
                  }}
                  briefing={{
                    paragraphs: [
                      `We'll conduct a pre-trip briefing to discuss the ${data.name} route, safety guidelines, equipment requirements, and what to expect during your ${data.duration} adventure.`,
                    ],
                  }}
                />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            {shortItinerary.length > 0 && (
              <section className="scroll-mt-[200px]">
                <ItinerarySummary
                  title={
                    data.shortItinerary?.title ||
                    `${data.name} – Short Itinerary`
                  }
                  days={shortItinerary}
                />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            {galleryItems.length > 0 && (
              <section id="gallery" className="scroll-mt-[200px]">
                <GalleryWithMore items={galleryItems} />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            {detailedItinerary.length > 0 && (
              <section id="itinerary" className="scroll-mt-[200px]">
                <ItineraryDetailed days={detailedItinerary} />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            <section id="map" className="scroll-mt-[200px]">
              <Mapandchart
                imageSrc={
                  data.map?.map || `/images/map/trekking/${data.slug}-map.webp`
                }
                altText={`${data.name} Route Map`}
              />
              <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
            </section>

            {(apiInclusions.length > 0 || apiExclusions.length > 0) && (
              <section id="inclusions" className="scroll-mt-[200px]">
                <TrekInclusionsExclusions
                  inclusions={apiInclusions}
                  exclusions={apiExclusions}
                />
                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
              </section>
            )}

            <section id="gears" className="scroll-mt-[200px]">
              {data.equipment?.length > 0 ? (
                <div className="bg-white p-6 rounded-lg">
                  <h2 className="text-xl md:text-3xl font-bold text-start text-brand mb-8">
                    Gears and Equipments
                  </h2>
                  <div className="space-y-6">
                    {data.equipment.map((equipment) => (
                      <div
                        key={equipment.id}
                        className="border-b border-gray-200 pb-4 last:border-b-0"
                      >
                        <h3 className="font-semibold text-lg mb-2">
                          {equipment.title}
                        </h3>
                        <p className="text-gray-600 mb-3">
                          {equipment.description}
                        </p>
                        <div className="grid md:grid-cols-3 gap-4 text-sm">
                          {equipment.head && (
                            <div>
                              <h4 className="font-medium mb-1 prose prose-scrollable-tables">
                                Head Gear
                              </h4>
                              <HtmlContentDisplay
                                htmlContent={equipment.head}
                              />
                            </div>
                          )}
                          {equipment.body && (
                            <div>
                              <h4 className="font-medium prose mb-1 prose-scrollable-tables">
                                Body Gear
                              </h4>
                              <HtmlContentDisplay
                                htmlContent={equipment.body}
                              />
                            </div>
                          )}
                          {equipment.face && (
                            <div>
                              <h4 className="font-medium mb-1 prose prose-scrollable-tables">
                                Face Protection
                              </h4>
                              <HtmlContentDisplay
                                htmlContent={equipment.face}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <Gears />
              )}
              <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
            </section>

            <section id="package-info" className="scroll-mt-[200px]">
              <div className="bg-white p-6 rounded-lg">
                <h2 className="text-xl md:text-3xl font-bold text-start text-brand mb-8">
                  Package Information
                </h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-2">{data.type} Details</h3>
                    <ul className="space-y-1 text-sm">
                      <li>
                        <strong>Duration:</strong> {data.duration}
                      </li>
                      <li>
                        <strong>Max Altitude:</strong> {data.altitude}m
                      </li>
                      <li>
                        <strong>Grade:</strong> {data.grade}
                      </li>
                      <li>
                        <strong>Group Size:</strong> {data.groupSize}
                      </li>
                      <li>
                        <strong>Activity:</strong> {data.activity.name}
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Logistics</h3>
                    <ul className="space-y-1 text-sm">
                      <li>
                        <strong>Transport:</strong> {data.transport}
                      </li>
                      <li>
                        <strong>Accommodation:</strong> {data.accomodation}
                      </li>
                      <li>
                        <strong>Meals:</strong> {data.meals}
                      </li>
                      <li>
                        <strong>Activities:</strong> {data.activityPerDay}
                      </li>
                    </ul>
                  </div>
                </div>

                {(data.info?.items || []).length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-xl md:text-3xl font-bold text-start text-brand mb-8">
                      {data.info?.title || 'Additional Information'}
                    </h3>
                    <div className="space-y-10">
                      {data.info?.items.map((item, index) => (
                        <div key={index} className="text-xl text-black">
                          <div className="font-bold text-brand">
                            {item.title}
                          </div>
                          {item.details && (
                            <div className="mt-1 text-gray-700">
                              <HtmlContentDisplay htmlContent={item.details} />
                            </div>
                          )}

                          {item.note && (
                            <div className={`${'bg-brand/10'} rounded-lg p-6`}>
                              <div className="flex items-start gap-3 mb-2">
                                <FileText
                                  className="w-6 h-6 text-brand flex-shrink-0"
                                  size={24}
                                />
                                <h3 className="text-lg font-semibold text-gray-800">
                                  Note
                                </h3>
                              </div>
                              <HtmlContentDisplay htmlContent={item.note} />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* {data.costDate?.filter((cd) => cd.published).length > 0 && (
                  <div className="mt-6">
                    <h3 className="font-semibold mb-3">
                      Available Dates & Pricing
                    </h3>
                    <div className="grid gap-3">
                      {data.costDate
                        .filter((cd) => cd.published)
                        .map((costDate) => (
                          <div
                            key={costDate.id}
                            className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                          >
                            <div>
                              <p className="font-medium">
                                {new Date(
                                  costDate.startDate
                                ).toLocaleDateString()}{' '}
                                -{' '}
                                {new Date(
                                  costDate.endDate
                                ).toLocaleDateString()}
                              </p>
                              <p className="text-sm text-gray-600">
                                {costDate.days} days • {costDate.tripStatus}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold">
                                {costDate.discountPrice}
                              </p>
                              {costDate.price !== costDate.discountPrice && (
                                <p className="text-sm text-gray-500 line-through">
                                  {costDate.price}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )} */}
              </div>
              <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
            </section>

            {(apiVideos.length > 0 || apiReviews.length > 0) && (
              <section id="reviews" className="scroll-mt-[200px]">
                {apiVideos.length > 0 && (
                  <>
                    <TravelersReview
                      videos={apiVideos}
                      onWatchMore={() =>
                        window.open('https://youtube.com/yourchannel', '_blank')
                      }
                    />
                    <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                  </>
                )}

                {apiReviews.length > 0 && (
                  <StackedReviews
                    reviews={apiReviews}
                    headerTitle="Customer Reviews"
                  />
                )}
              </section>
            )}
          </div>
        </div>
      </div>

      {faqSchema && (
        <script
          key="faq-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
        />
      )}

      {data.faq?.filter((faq) => faq.published)?.length > 0 && (
        <div className="container mx-auto px-2 md:px-4 pb-8">
          <div className="bg-white p-6 rounded-lg">
            <h2 className="text-xl md:text-3xl font-bold text-start text-brand mb-8">
              Frequently Asked Questions
            </h2>
            <Accordion type="multiple" className="w-full">
              {data.faq
                .filter((faq) => faq.published)
                .map((faq, index) => (
                  <AccordionItem key={faq.id} value={faq.id}>
                    <AccordionTrigger>
                      {index + 1}. {faq.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <HtmlContentDisplay
                        htmlContent={faq.answer}
                        className="prose"
                      />
                    </AccordionContent>
                  </AccordionItem>
                ))}
            </Accordion>
          </div>
        </div>
      )}

      {/* <FAQSection /> */}
    </>
  );
};

export default TrekDetailPage;
