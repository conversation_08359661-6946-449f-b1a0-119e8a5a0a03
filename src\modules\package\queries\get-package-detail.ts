import { useQuery } from '@tanstack/react-query';
import { IPackage } from '@/types/package';
import { IApiResponse } from '@/types/response';

const fetchPackageDetail = async (
  slug: string
): Promise<IApiResponse<IPackage>> => {
  const response = await fetch(
    `https://api.trailandtreknepal.com/package/slug/${slug}`,
    {
      mode: 'cors',
    }
  );

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch package details');
  }

  return data;
};

export const usePackageDetail = (slug: string) => {
  return useQuery<IApiResponse<IPackage>, Error>({
    queryKey: ['packageDetail', slug],
    queryFn: () => fetchPackageDetail(slug),
    enabled: !!slug, // Only run query if slug exists
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (formerly cacheTime)
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};
