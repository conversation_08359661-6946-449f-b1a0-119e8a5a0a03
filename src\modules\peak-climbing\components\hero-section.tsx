import { Badge } from '@/components/ui/badge'
import { Calendar, MapPin, Star, TrendingUp } from 'lucide-react'
import React from 'react'

const PeakClimbingHeroSection = () => {
    return (
        <div className="min-h-screen bg-white">
            <section
                className="relative h-[80vh] bg-cover bg-center bg-no-repeat"
                style={{
                    backgroundImage: "url('/images/fastpacking/hero/image1.webp')"
                }}
            >
                <div className="absolute inset-0 bg-gradient-to-b from-dark/10 via-dark/20 to-dark"></div>
                <div className="relative z-10 container mx-auto px-4 h-full flex flex-col justify-end items-end">
                    <div className="text-white">
                        <div className="flex items-center space-x-4 mb-6">
                            <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                                <Star className="h-4 w-4 mr-1 fill-current" />
                                5.0
                            </Badge>
                            <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                                15 reviews
                            </Badge>
                        </div>

                        <h1 className="text-6xl font-bold mb-6 tracking-tight">CLIMB MERA PEAK (6461m)</h1>

                        <div className="flex flex-wrap items-center gap-4 mb-6">
                            <Badge variant="secondary" className="bg-green-600 text-white hover:bg-green-700">
                                <MapPin className="h-4 w-4 mr-1" />
                                NEPAL
                            </Badge>
                            <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                                <Calendar className="h-4 w-4 mr-1" />
                                18 NIGHTS
                            </Badge>
                            <Badge variant="secondary" className="bg-white/20 text-white hover:bg-white/30">
                                <TrendingUp className="h-4 w-4 mr-1" />
                                TOUGH
                            </Badge>
                        </div>

                        <p className="text-2xl mb-8 leading-relaxed">
                            Conquer the highest trekking peak in Nepal and enjoy epic views of the world&apos;s tallest mountains,
                            including mighty Everest itself
                        </p>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default PeakClimbingHeroSection