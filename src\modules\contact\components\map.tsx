import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import React from 'react'

const ContactMap = () => {
    return (
        <div className="container mx-auto px-4">
            <div className="mt-12">
                <Card className="border-0 shadow-lg overflow-hidden">
                    <CardHeader className="pb-0">
                        <CardTitle className="text-2xl text-center">Find Us on Google Maps</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 mt-4">
                        <div className="h-96 w-full">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d11330.821722458022!2d83.95706771364794!3d28.207691132809412!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3995951c1e27b3f9%3A0xdc8b48fb01c20eac!2sNorth%20Nepal%20Travel%20%26%20Trek%20Pvt.%20Ltd!5e1!3m2!1sen!2snp!4v1755427462975!5m2!1sen!2snp"
                                width="100%"
                                height="100%"
                                className="border-none"
                                allowFullScreen={true}
                                referrerPolicy="no-referrer-when-downgrade"
                                title="Trek and Trail Location"
                            />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default ContactMap