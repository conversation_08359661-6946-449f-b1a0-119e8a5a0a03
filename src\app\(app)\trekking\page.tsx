import getPackagesByActivitySlug from '@/actions/package/get-packages-by-activity';
import PackageCard from '@/components/common/cards/card';
import Link from 'next/link';
import React from 'react';

const TrekkingPage = async () => {
  const packages = await getPackagesByActivitySlug('trekking');
  return (
    <main className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-8">Trekking</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {packages.data.map((pkg) => (
          <Link href={`/trekking/${pkg.slug}`} key={pkg.id}>
            <PackageCard key={pkg.id} package={pkg} />
          </Link>
        ))}
      </div>
    </main>
  );
};

export default TrekkingPage;
