"use client"

import React, { useRef, useState } from "react"
import {
    Video as VideoIcon,
    ChevronLeft,
    ChevronRight,
    PlayCircle,
} from "lucide-react"
import Image from "next/image"

export interface ReviewVideo {
    id: string
    title: string
    youtubeId: string
}

interface TravelersReviewProps {
    videos: ReviewVideo[]
    onWatchMore?: () => void
}

export default function TravelersReview({
    videos,
}: TravelersReviewProps) {
    const listRef = useRef<HTMLDivElement>(null)
    const [playingId, setPlayingId] = useState<string | null>(null)

    const scrollBy = (distance: number) => {
        if (listRef.current) {
            listRef.current.scrollBy({ left: distance, behavior: "smooth" })
        }
    }

    return (
        <>
            <section className="container mx-auto px-2 md:px-4 ">
                <div className="mb-2">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                            <VideoIcon size={16} className="text-light" />
                        </div>
                        <h2 className="text-2xl md:text-3xl font-bold text-brand">Travelers&apos; Review Video</h2>
                    </div>
                </div>
                
                {/* Carousel */}
                <div className="relative">
                    <button
                        onClick={() => scrollBy(-300)}
                        className="absolute left-0 top-1/2 -translate-y-1/2 z-10 rounded-full bg-white p-2 shadow"
                    >
                        <ChevronLeft className="w-5 h-5 text-gray-700" />
                    </button>

                    <div
                        ref={listRef}
                        className="flex space-x-4 overflow-x-auto scrollbar-hide scroll-snap-x snap-mandatory px-8"
                    >
                        {videos.map((v) => (
                            <div
                                key={v.id}
                                onClick={() => setPlayingId(v.youtubeId)}
                                className="snap-start flex-shrink-0 w-full sm:w-1/2 md:w-1/3 cursor-pointer group "
                            >
                                <div className="relative h-56 w-full overflow-hidden rounded-lg mb-2">
                                    <Image
                                        src={`https://img.youtube.com/vi/${v.youtubeId}/hqdefault.jpg`}
                                        alt={v.title}
                                        fill
                                        className="object-cover w-full h-full"
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <PlayCircle className="w-12 h-12 text-white" />
                                    </div>
                                </div>
                                {/* <p className="mt-2 text-sm font-medium text-gray-800">{v.title}</p> */}
                            </div>
                        ))}
                    </div>

                    <button
                        onClick={() => scrollBy(300)}
                        className="absolute right-0 top-1/2 -translate-y-1/2 z-10 rounded-full bg-white p-2 shadow"
                    >
                        <ChevronRight className="w-5 h-5 text-gray-700" />
                    </button>
                </div>
            </section>

            {/* Lightbox */}
            {playingId && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
                    <div className="relative w-full max-w-3xl aspect-video">
                        <iframe
                            className="w-full h-full rounded-lg"
                            src={`https://www.youtube.com/embed/${playingId}?autoplay=1&mute=1`}
                            allow="autoplay; encrypted-media; picture-in-picture"
                            allowFullScreen
                        />
                    </div>
                    <button
                        onClick={() => setPlayingId(null)}
                        className="absolute top-6 right-6 text-white text-4xl font-bold"
                    >
                        &times;
                    </button>
                </div>
            )}
        </>
    )
}
