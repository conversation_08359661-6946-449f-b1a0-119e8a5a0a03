import { cn } from '@/lib/utils';

const HtmlContentDisplay: React.FC<{
  htmlContent: string;
  className?: string;
}> = ({ htmlContent, className = '' }) => {
  return (
    <div className={cn('html-content-wrapper max-w-none prose', className)}>
      <div
        className="prose prose-sm max-w-none"
        dangerouslySetInnerHTML={{
          __html: htmlContent
            .replace(/<table/g, '<div class="table-container"><table')
            .replace(/<\/table>/g, '</table></div>'),
        }}
      />
    </div>
  );
};

export default HtmlContentDisplay;
