"use client"

import React from "react"
import {
    <PERSON><PERSON><PERSON>,
    Repeat,
    Dumbbell,
    Activity,
    Footprints,
    DumbbellIcon,
} from "lucide-react"

interface Section {
    key: string
    title: string
    Icon: React.ElementType
    items: string[]
}

const sections: Section[] = [
    {
        key: "endurance",
        title: "Endurance",
        Icon: Repeat,
        items: [
            "Build a solid aerobic base by training 4–5 days per week for 30–90 minutes (running, hiking, cycling or incline treadmill).",
            "As the trek approaches, implement longer workouts (2–4 hrs) carrying a loaded daypack and ascending steep terrain.",
            "Aim to cover 8–12 miles daily while gaining ≥ 1 000 m to simulate Nepal’s mountain trails.",
        ],
    },
    {
        key: "strength",
        title: "Strength",
        Icon: Dumbbell,
        items: [
            "Complement endurance with lower-body & core strength 2–3 days/week: squats, lunges, deadlifts, planks, toes-to-bar.",
            "Use added weight or resistance bands to condition muscles for carrying a fast-packing pack uphill.",
        ],
    },
    {
        key: "balance",
        title: "Balance",
        Icon: Activity,
        items: [
            "Practice single-leg deadlifts and pistol squats to mimic stabilizing steps on uneven ground.",
            "Include lateral hops and agility drills for micro-adjustments on narrow ridgelines.",
        ],
    },
    {
        key: "running-practice",
        title: "Running Practice",
        Icon: Footprints,
        items: [
            "Break-in your exact trail shoes on both road and dirt surfaces.",
            "Train fully loaded: pack all gear (clothes, sleeping bag) and run short loops.",
            "Simulate a full fast-packing day once: distance + elevation with a fully loaded pack.",
        ],
    },
]

export default function PreparationAndTraining() {
    return (
        <div className="container mx-auto px-2 md:px-4 space-y-8">
            <div className="mb-2">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                        <DumbbellIcon size={16} className="text-light" />
                    </div>
                    <h2 className="text-2xl md:text-3xl font-bold text-brand">Physical Preparation and Training</h2>
                </div>
            </div>

            <div className="space-y-4">
                {sections.map(({ key, title, Icon, items }) => (
                    <div key={key} className="bg-light rounded-lg mb-10">
                        <div className="flex items-center gap-3 mb-4">
                            <Icon className="w-6 h-6 text-dark" />
                            <h3 className="text-xl md:text-2xl font-semibold text-gray-900">
                                {title}
                            </h3>
                        </div>
                        <ul className="space-y-1">
                            {items.map((text, i) => (
                                <li key={i} className="flex items-start gap-3">
                                    <ArrowRight
                                        size={24}
                                        className="text-dark mt-0.5 flex-shrink-0"
                                    />
                                    <span className="text-gray-700 leading-relaxed">{text}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </div>
        </div>
    )
}
