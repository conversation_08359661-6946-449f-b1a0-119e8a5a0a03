'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Divider from './divider';
import SearchBar from './search-bar';
import { IHomeHero } from '@/types/home';
import { Play, X } from 'lucide-react';

const HeroSection = ({ hero }: { hero: IHomeHero }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVideoOpen, setIsVideoOpen] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % hero.images.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [hero.images.length]);

  return (
    <div className="relative h-[75vh] md:h-screen min-h-[300px] overflow-hidden">
      <div className="hidden md:block absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          <source src={hero.videoUrl} type="video/mp4" />
        </video>
      </div>

      <div className="md:hidden absolute inset-0 overflow-hidden">
        <div
          className="flex transition-transform duration-1000 ease-in-out h-full"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {hero.images.map((image, index) => (
            <div key={index} className="relative min-w-full h-full">
              <Image
                src={image}
                alt={`Hero Image ${index + 1}`}
                fill
                className="object-cover"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="absolute inset-0 bg-dark/10"></div>
      <Divider />

      <div className="relative z-20 flex h-full items-center justify-center">
        <div className="max-w-4xl mx-auto px-4 md:px-8 lg:px-12 text-center">
          <button
            onClick={() => setIsVideoOpen(true)}
            aria-label="Play hero video"
            className="group rounded-full border-4 border-white p-2 sm:p-2 md:p-[11px] bg-transparent transition-colors duration-300 hover:border-red-600 w-10 h-10 sm:w-12 sm:h-12 md:w-15 md:h-15"
          >
            <Play className="text-white fill-white hover:fill-red-600 group-hover:text-red-600 w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8" />
          </button>

          <h1
            className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-medium text-light mb-4 font-serif italic leading-tight"
            style={{
              textShadow: '3px 3px 6px rgba(94, 163, 0, 0.5)',
            }}
          >
            {hero.titles[0]}
          </h1>

          <p className="text-base sm:text-lg md:text-xl text-light/90 mb-8 max-w-2xl mx-auto leading-relaxed">
            {hero.subtitles[0]}
          </p>
          
          <SearchBar />
        </div>
      </div>

      {isVideoOpen && (
        <div
          className="fixed inset-0 bg-black/75 flex items-center justify-center z-50"
          onClick={() => setIsVideoOpen(false)}
        >
          <div
            className="relative w-full max-w-3xl mx-4 aspect-video bg-black"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setIsVideoOpen(false)}
              className="absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white z-10"
              aria-label="Close video"
            >
              <X className="h-5 w-5" />
            </button>

            <iframe
              className="absolute inset-0 w-full h-full"
              src={`https://www.youtube.com/embed/${hero.youtubeUrl.split('v=')[1]
                }?autoplay=1&modestbranding=1&rel=0`}
              title="Hero Video"
              allow="autoplay; encrypted-media; picture-in-picture"
              allowFullScreen
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default HeroSection;
