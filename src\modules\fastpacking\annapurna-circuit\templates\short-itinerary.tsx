import React from 'react';
import { Navigation } from 'lucide-react';

const ItineraryTable = () => {
  const itineraryData = [
    {
      day: 1,
      title: "Drive to Chame",
      distance: "271 km total drive",
      duration: "9-13.5 hours",
      description: "Drive from Pokhara to Besisahar (4 hours, 105 km) OR from Kathmandu to Besisahar (5.5 hours, 180 km), then drive from Besisahar to Chame (4-5 hours, 66 km)",
      altitudes: "Pokhara: 820m • Kathmandu: 1,400m • Besisahar: 760m • Chame: 2,650m"
    },
    {
      day: 2,
      title: "Fastpack from Chame to Manang",
      distance: "25 km",
      duration: "7-8 hours",
      description: "Fastpacking through beautiful mountain terrain with stunning views",
      altitudes: "Chame: 2,650m • Manang: 3,519m"
    },
    {
      day: 3,
      title: "Run from Manang to Thorung Phedi",
      distance: "18 km",
      duration: "6-7 hours",
      description: "Fast-paced run through high altitude terrain approaching Thorung La Pass",
      altitudes: "Manang: 3,519m • Thorung Phedi: 4,540m"
    },
    {
      day: 4,
      title: "Thorung Phedi to Muktinath via Thorung La Pass",
      distance: "17 km",
      duration: "6-7 hours",
      description: "Hiking during the first half till Thorung La Pass, then running till Muktinath",
      altitudes: "Thorung Phedi: 4,540m • Thorung La Pass: 5,416m • Muktinath: 3,710m"
    },
    {
      day: 5,
      title: "Drive from Muktinath to Pokhara",
      distance: "174 km drive",
      duration: "6-7 hours",
      description: "Scenic drive back to Pokhara. Total walking distance: 60km, Total driving: 345km",
      altitudes: "Muktinath: 3,710m • Pokhara: 820m"
    }
  ];

  return (
    <div className="mt-12">
      <div className="flex items-center gap-3 mb-8">
        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
          <Navigation size={16} className="text-light" />
        </div>
        <h2 className="text-3xl font-bold text-brand">Shorter Itinerary</h2>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse bg-light rounded-lg shadow-sm border border-brand/50 table-fixed">
          <thead>
            <tr className="bg-brand/80">
              <th className="border border-light px-2 py-1 text-left font-semibold text-light w-16">Day</th>
              <th className="border border-light px-2 py-1 text-left font-semibold text-light w-90">Route</th>
              <th className="border border-light px-2 py-1 text-left font-semibold text-light w-28">Distance</th>
              <th className="border border-light px-2 py-1 text-left font-semibold text-light w-28">Duration</th>
              <th className="border border-light px-2 py-1 text-left font-semibold text-light w-45">Altitudes</th>
            </tr>
          </thead>
          <tbody>
            {itineraryData.map((day) => (
              <tr key={day.day} className="hover:bg-gray-50 transition-colors">
                <td className="border border-brand/50 px-2 py-1">
                  <div className="flex flex-col items-center">
                    <span className="text-sm text-brand font-medium">Day</span>
                    <span className="text-xl font-bold text-brand">{day.day}</span>
                  </div>
                </td>
                <td className="border border-brand/50 px-2 py-1">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-gray-900">{day.title}</h3>
                    <p className="text-sm text-gray-600 leading-relaxed">{day.description}</p>
                  </div>
                </td>
                <td className="border border-brand/50 px-2 py-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{day.distance}</span>
                  </div>
                </td>
                <td className="border border-brand/50 px-2 py-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{day.duration}</span>
                  </div>
                </td>
                <td className="border border-brand/50 px-2 py-1">
                  <div className="text-sm text-gray-600 leading-relaxed space-y-1">
                    {day.altitudes.split(' • ').map((altitude, index) => (
                      <div key={index} className="flex items-center gap-1">
                        <span>{altitude}</span>
                      </div>
                    ))}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ItineraryTable;