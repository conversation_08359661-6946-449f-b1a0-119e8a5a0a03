import { Button } from '@/components/ui/button';
import { IPackage } from '@/types/package';
import { Clock, Star } from 'lucide-react';
import Image from 'next/image';

interface PackageCardProps {
  package: IPackage;
}

const PackageCard: React.FC<PackageCardProps> = ({ package: pkg }) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="bg-light overflow-hidden hover:shadow-2xl transition-all group">
      <div className="relative h-64 w-full overflow-hidden">
        <Image
          src={pkg.thumbnail}
          alt={pkg.name}
          fill
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        />
      </div>
      <div className="p-4">
        <h3 className="text-xl font-bold text-dark/90 mb-3 leading-tight">
          {pkg.name}
        </h3>
        <div className="flex items-center justify-between gap-2 space-y-2">
          <div className=" flex items-center gap-2">
            <Clock className="w-4 h-4 text-brand" />
            <span className="text-dark/70 text-sm">{pkg.duration}</span>
          </div>
          <div className="">
            <span className="text-dark/70 line-through text-sm">
              {pkg.price}
            </span>
          </div>
        </div>
        <div className="flex items-center justify-between gap-2 space-y-2">
          <div className="flex items-center gap-1 mb-4">{renderStars(5)}</div>
          <div className="flex items-end justify-between mb-4">
            <div className="text-2xl font-bold text-brand">
              {pkg.discountPrice}
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-center mb-4">
        <Button className="w-50 text-white">View More</Button>
      </div>
    </div>
  );
};

export default PackageCard;
