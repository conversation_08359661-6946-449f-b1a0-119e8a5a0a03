"use client"

import React from "react"
import { Footprints, Moon, Setting<PERSON>, CheckCircle, Shirt, ShoppingBag, Settings2 } from "lucide-react"

const sections = [
  {
    key: "clothing-top",
    title: "Clothing Top",
    Icon: Shirt,
    items: [
      "Sunglasses",
      "Beanie",
      "Sunhat",
      "Sunscreen",
      "Bandana",
      "Headlamp or Flashlight",
    ],
  },
  {
    key: "clothing-bottom",
    title: "Clothing Bottom",
    Icon: ShoppingBag,
    items: [
      "Long & short sleeve shirts (merino or synthetic)",
      "Light puffy jacket + down jacket",
      "Rain shell & wind shell jackets",
      "Running shorts, tech pants, puffy pants",
      "Sleeping bag",
      "Many pairs of underwear",
    ],
  },
  {
    key: "feet",
    title: "Feet",
    Icon: Footprints,
    items: [
      "Trail running shoes",
      "2–3 pairs of socks + thicker camp socks",
      "Chemical warmers for high passes",
      "Crampons or microspikes",
    ],
  },
  {
    key: "sleeping",
    title: "Sleeping",
    Icon: Moon,
    items: [
      "Sleeping bag rated to –20 °C",
      "Sleeping liners",
      "No tent needed (tea-house stays)",
    ],
  },
  {
    key: "miscellaneous",
    title: "Miscellaneous",
    Icon: Settings,
    items: [
      "30 L lightweight pack",
      "Water bottles & bladder",
      "Travel medicines",
      "Personal hygiene kit",
      "Nepali cash",
      "Passport or copy",
      "Emergency contact sheet",
      "Compass with sighting mirror",
    ],
  },
]

export default function Gears() {
  return (
    <div className="container mx-auto px-2 md:px-4 space-y-8">
      <div className="mb-2">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
            <Settings2 size={16} className="text-light" />
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-brand">Gears and Accessories</h2>
        </div>

        <p className="text-gray-700 mb-4">
          The main concept of fastpacking is to carry lighter bag will help you travel further distance in lesser time. Fastpacking gears helps trekkers to run and walk cover multiple days. By fitting all necessary supplies into a lightweight backpack, you can travel faster and cover greater distances, enabling you to explore more without needing to return to a fixed base each night.
        </p>
      </div>

      {sections.map(({ key, title, Icon, items }) => (
        <div key={key} className="bg-light rounded-lg">
          <div className="flex items-center gap-3 mb-4">
            <Icon className="w-6 h-6 text-dark" />
            <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          </div>
          <ul className="space-y-1">
            {items.map((text, i) => (
              <li key={i} className="flex items-start gap-2">
                <CheckCircle className="w-5 h-5 text-dark flex-shrink-0 mt-1" />
                <span className="text-gray-800">{text}</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}
