import Image from 'next/image';
import Link from 'next/link';
import { FaFacebookF, FaTwitter, FaInstagram } from 'react-icons/fa';

export default function Footer() {
  return (
    <div>
      <div className=" w-full overflow-hidden">
        <Image
          src="/images/footer/mountain-banner.png"
          alt="Himalayan mountain panorama"
          width={1920}
          height={0}
          priority
          className="object-cover"
        />
      </div>
      <footer className="bg-[#262626] text-gray-100 px-6 py-10">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* LEFT COLUMN */}
            <div>
              {/* BOOK WITH CONFIDENCE */}
              <div className="mb-12">
                <h3 className="font-bold text-lg mb-2">BOOK WITH CONFIDENCE</h3>
                <p className="mb-4 text-sm">
                  Find out how your booking with <PERSON> and <PERSON> is protected
                  through our ABTOT membership
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <Image
                    src="/images/footer/abtot-logo-white.png"
                    alt="ABTOT"
                    width={100}
                    height={100}
                    className="h-11 w-auto"
                  />
                  <Image
                    src="/images/footer/abta-logo-white.png"
                    alt="ABTA"
                    width={100}
                    height={100}
                    className="h-11 w-auto"
                  />
                  <Image
                    src="/images/footer/atol-logo-white.png"
                    alt="ATOL"
                    width={50}
                    height={100}
                    className="h-11 w-auto"
                  />
                </div>
              </div>

              <div>
                <h3 className="font-bold text-lg mb-2">POSITIVE IMPACT ADVENTURE TRAVEL</h3>
                <div className="flex items-center mt-2">
                  <div className="mr-7">
                    <Image
                      src="/images/footer/bcorp-logo-white.png"
                      alt="B Corp"
                      width={300}
                      height={600}
                      className="h-32 "
                    />
                  </div>
                  <div>
                    <p className="text-sm">
                      Responsible travel has always been at the core of what we do. Travelling
                      with Trail and Trek means not just better trips for you,
                      it&apos;s better for local communities, better for wildlife and better
                      for the planet.
                    </p>
                    <a href="#" className="text-green-400 underline text-sm mt-1 inline-block">
                      Learn More
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-5">

              <div>
                <h4 className="font-bold mb-2">Explore Adventures</h4>
                <ul className="space-y-2 text-sm">
                  <li className="hover:underline"><Link href="/trail-running">Trail Running</Link></li>
                  <li className="hover:underline"><Link href="/peak-climbing">Peak Climbing</Link></li>
                  <li className="hover:underline"><Link href="#">Training Programs</Link></li>
                  <li className="hover:underline"><Link href="/about">About Us</Link></li>
                  <li className="hover:underline"><Link href="/contact">Contact Us</Link></li>
                  <li className="hover:underline"><Link href="/blogs">Blogs</Link></li>
                </ul>
              </div>

              <div>
                <h4 className="font-bold mb-2">Travel Resources</h4>
                <ul className="space-y-2 text-sm">
                  <li className="hover:underline"><Link href="#">Packing List for Nepal Treks</Link></li>
                  <li className="hover:underline"><Link href="#">Trekking Permit Information</Link></li>
                  <li className="hover:underline"><Link href="#">Weather & Season Guide</Link></li>
                  <li className="hover:underline"><Link href="#">Altitude and Acclimatization Tips</Link></li>
                  <li className="hover:underline"><Link href="#">Payments & Cancellation Policy</Link></li>
                </ul>
              </div>

              {/* <div>
                <h4 className="font-bold mb-2">TRAVEL COMPANIES</h4>
                <ul className="space-y-2 text-sm">
                  <li className="hover:underline"><Link href="#">Host Knowledge Base</Link></li>
                  <li className="hover:underline"><Link href="#">Apply to Host</Link></li>
                </ul>
              </div> */}

              <div>
                <h4 className="font-bold mb-2">PARTNERS</h4>
                <ul className="space-y-2 text-sm mb-4">
                  <li className="hover:underline"><Link href="#">Media</Link></li>
                  <li className="hover:underline"><Link href="#">Partnerships</Link></li>
                </ul>
                <button className="border border-gray-100 px-6 py-2 rounded-lg font-bold hover:bg-gray-800 transition">
                  GBP
                </button>
              </div>

            </div>
          </div>

          {/* Bottom */}
          <div className="mt-8 border-t border-gray-700 pt-6 flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2">
              <Image src="/images/logo/fastpacking-logo.png" alt="Trek and Trail Logo" width={200} height={100} className="h-8 w-auto" />
              <span className="text-sm">
                © Copyright ©2025 Trail and Trek, All Rights Reserved
              </span>
            </div>
            <div className="flex space-x-5 mt-4 md:mt-0">
              <Link href="#"><FaFacebookF size={32} /></Link>
              <Link href="#"><FaTwitter size={32} /></Link>
              <Link href="#"><FaInstagram size={32} /></Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
