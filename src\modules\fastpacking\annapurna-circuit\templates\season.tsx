import React from 'react'
import { <PERSON>, Sun, Snowflake, <PERSON>, Flower } from 'lucide-react';

const SeasonDetail = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <Calendar size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-brand">Season to Visit</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-light border-2 border-brand rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-brand rounded-full flex items-center justify-center">
                            <Flower size={20} className="text-light" />
                        </div>
                        <div>
                            <h3 className="text-xl font-bold text-brand">Annapurna Circuit Fastpacking in Spring</h3>
                            <p className="text-brand/80 font-medium">(March to May)</p>
                        </div>
                    </div>
                    <ul className="space-y-2 text-dark">
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Moderate temperatures and clear skies</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Rhododendron forests in full bloom</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Some snow on Thorung La pass</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Occasional rainfall in lower regions</span>
                        </li>
                    </ul>
                </div>

                <div className="bg-light border-2 border-brand rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-brand rounded-full flex items-center justify-center">
                            <Sun size={20} className="text-light" />
                        </div>
                        <div>
                            <h3 className="text-xl font-bold text-brand">Annapurna Circuit Fastpacking in Autumn</h3>
                            <p className="text-brand/80 font-medium">(Mid-September to November)</p>
                        </div>
                    </div>
                    <ul className="space-y-2 text-dark">
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Clear sky, warm days, cold nights</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Thorung La Pass is usually snow-free</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Post-monsoon trails can be muddy</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Harvest festivals in villages</span>
                        </li>
                    </ul>
                </div>

                <div className="bg-light border-2 border-brand rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-brand rounded-full flex items-center justify-center">
                            <Cloud size={20} className="text-light" />
                        </div>
                        <div>
                            <h3 className="text-xl font-bold text-brand">Annapurna Circuit Fastpacking in Summer</h3>
                            <p className="text-brand/80 font-medium">(June to August)</p>
                        </div>
                    </div>
                    <ul className="space-y-2 text-dark">
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Warm but wet days with leeches</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Landslides and risks of avalanches</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>The challenge for experienced fast packers</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Isolated trails good for pure adventure seekers</span>
                        </li>
                    </ul>
                </div>

                <div className="bg-light border-2 border-brand rounded-lg p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                            <Snowflake size={20} className="text-light" />
                        </div>
                        <div>
                            <h3 className="text-xl font-bold text-brand">Annapurna Circuit Fastpacking in Winter</h3>
                            <p className="text-gray-600 font-medium">(December to February)</p>
                        </div>
                    </div>
                    <ul className="space-y-2 text-dark">
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Heavy snowfall, high avalanche risk</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Extreme cold at higher passes</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Thorung La may be impassable due to snow</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Appeal only for hardcore fastpackers</span>
                        </li>
                        <li className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-brand rounded-full mt-2 flex-shrink-0"></span>
                            <span>Requires extensive gear and guidance</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    )
}

export default SeasonDetail