import React from "react";
import { Navigation, Clock } from "lucide-react";
import { DayDetail } from "@/modules/fastpacking/components/itinerary-detailed";

export const detailedKhopraRidgeItinerary: DayDetail[] = [
  {
    day: 1,
    title: "Arrival in Kathmandu",
    stats: [],
    progressPct: 10,
    description: (
      <>
        <p>
          After your arrival at Kathmandu, one of our representatives will pick you up from the airport and transfer you to the hotel. Rest and get prepared for your mountain journey.
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,400 m" }],
  },
  {
    day: 2,
    title: "Kathmandu → Pokhara",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive Duration", value: "6–7 hrs / 25 min flight" },
    ],
    progressPct: 20,
    description: (
      <>
        <p>
          Drive or fly to Pokhara. Enjoy the evening around Lakeside and relax before the trek begins.
        </p>
      </>
    ),
    altitudes: [
      { label: "Kathmandu", value: "1,400 m" },
      { label: "Pokhara", value: "822 m" },
    ],
  },
  {
    day: 3,
    title: "Drive from Pokhara to Ghandruk, trek to Tadapani",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Trek Distance", value: "6.5 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "4–5 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "1,808 m" },
    ],
    progressPct: 30,
    description: (
      <>
        <p>
          Drive for 2–3 hours to Ghandruk, a beautiful Gurung village. Start your trek to Tadapani through lush rhododendron and oak forests with glimpses of the Annapurna range.
        </p>
      </>
    ),
    altitudes: [
      { label: "Ghandruk", value: "1,940 m" },
      { label: "Tadapani", value: "2,630 m" },
    ],
  },
  {
    day: 4,
    title: "Tadapani to Dobato",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "6.5 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "5 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "720 m" },
    ],
    progressPct: 40,
    description: (
      <>
        <p>
          Trek through rhododendron forests and ascending trails to Dobato. Enjoy spectacular views of Annapurna and Dhaulagiri ranges from this peaceful and less-crowded stop.
        </p>
      </>
    ),
    altitudes: [
      { label: "Tadapani", value: "2,630 m" },
      { label: "Dobato", value: "3,350 m" },
    ],
  },
  {
    day: 5,
    title: "Dobato to Chistibung",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "7 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "5 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Loss", value: "350 m" },
    ],
    progressPct: 50,
    description: (
      <>
        <p>
          After a morning hike to Mulde View Point, descend through forests and ridges to reach Chistibung, a small hamlet with warm Magar and Gurung hospitality.
        </p>
      </>
    ),
    altitudes: [
      { label: "Dobato", value: "3,350 m" },
      { label: "Chistibung", value: "3,000 m" },
    ],
  },
  {
    day: 6,
    title: "Chistibung → Khopra Ridge, descend to Pode Kharka",
    stats: [
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "650 m" },
    ],
    progressPct: 60,
    description: (
      <>
        <p>
          Ascend steeply along the ridge to reach Khopra Danda, offering panoramic views of Dhaulagiri, Nilgiri, and Annapurna South. After exploring, descend to Pode Kharka for overnight.
        </p>
      </>
    ),
    altitudes: [
      { label: "Chistibung", value: "3,000 m" },
      { label: "Khopra Ridge", value: "3,640 m" },
      { label: "Pode Kharka", value: "3,650 m" },
    ],
  },
  {
    day: 7,
    title: "Pode Kharka → Hidden Lake, trek to Tiribung",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "26 km approx" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7–8 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Loss", value: "220 m" },
    ],
    progressPct: 70,
    description: (
      <>
        <p>
          Trek 2–3 hours to reach Hidden Lake (4,250 m), a sacred, self-sustaining lake with breathtaking surroundings. After exploration, descend a steep trail and climb back to Tiribung.
        </p>
      </>
    ),
    altitudes: [
      { label: "Hidden Lake", value: "4,250 m" },
      { label: "Tiribung", value: "3,430 m" },
    ],
  },
  {
    day: 8,
    title: "Tiribung → Jhinu Danda, drive back to Pokhara",
    stats: [
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7–8 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Loss", value: "2,608 m" },
    ],
    progressPct: 80,
    description: (
      <>
        <p>
          Trek through Chhomrong to Jhinu Danda (1,780 m), known for natural hot springs. After a relaxing soak, drive back to Pokhara in the afternoon.
        </p>
      </>
    ),
    altitudes: [
      { label: "Tiribung", value: "3,430 m" },
      { label: "Jhinu Danda", value: "1,780 m" },
      { label: "Pokhara", value: "822 m" },
    ],
  },
  {
    day: 9,
    title: "Pokhara → Kathmandu",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs / 25 min flight" },
    ],
    progressPct: 90,
    description: (
      <>
        <p>
          Return to Kathmandu by flight or bus. Enjoy the rest of the day at leisure, shopping, or exploring the city before your departure.
        </p>
      </>
    ),
    altitudes: [
      { label: "Pokhara", value: "822 m" },
      { label: "Kathmandu", value: "1,400 m" },
    ],
  },
  {
    day: 10,
    title: "Final Departure from Kathmandu",
    stats: [],
    progressPct: 100,
    description: (
      <>
        <p>
          Our representative will drop you at the airport for your scheduled flight. Thank you for trekking with us!
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,400 m" }],
  },
];
