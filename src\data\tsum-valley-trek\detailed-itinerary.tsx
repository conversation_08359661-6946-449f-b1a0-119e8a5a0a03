import React from "react";
import { Navigation, Clock } from "lucide-react";
import { DayDetail } from "@/modules/fastpacking/components/itinerary-detailed";

export const detailedTsumValleyItinerary: DayDetail[] = [
  {
    day: 1,
    title: "Arrival at Kathmandu",
    stats: [],
    progressPct: 8,
    description: (
      <>
        <p>
          After your arrival at Kathmandu, one of our representatives will pick you up and transfer you to the hotel. Rest and prepare for your trek ahead.
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,324 m" }],
  },
  {
    day: 2,
    title: "Drive from Kathmandu/Pokhara to Machhakhola",
    stats: [
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive Duration", value: "8–10 hrs" },
    ],
    progressPct: 17,
    description: (
      <>
        <p>
          Begin your journey with a scenic drive through hills and river valleys, heading toward Machhakhola, a small village beside the Budhi Gandaki River.
        </p>
      </>
    ),
    altitudes: [{ label: "Machhakhola", value: "870 m" }],
  },
  {
    day: 3,
    title: "Trek from Machhakhola to Jagat",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "22 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7 hrs" },
    ],
    progressPct: 25,
    description: (
      <>
        <p>
          Trek along the Budhi Gandaki River, passing small villages, suspension bridges, and waterfalls. The trail gradually ascends to the village of Jagat.
        </p>
      </>
    ),
    altitudes: [{ label: "Jagat", value: "1,300 m" }],
  },
  {
    day: 4,
    title: "Jagat to Lokpa",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "16 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7–8 hrs" },
    ],
    progressPct: 33,
    description: (
      <>
        <p>
          Enter the Tsum Valley region as you hike through dense forests and charming villages. Cross a suspension bridge and climb towards Lokpa, your peaceful stop for the night.
        </p>
      </>
    ),
    altitudes: [{ label: "Lokpa", value: "2,240 m" }],
  },
  {
    day: 5,
    title: "Lokpa to Gho Village",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "17 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
    ],
    progressPct: 42,
    description: (
      <>
        <p>
          Trek deeper into the remote valley. Cross wooden bridges, walk beside the Syar River, and enjoy views of Ganesh Himal as you reach the quiet Gho Village.
        </p>
      </>
    ),
    altitudes: [{ label: "Gho", value: "2,415 m" }],
  },
  {
    day: 6,
    title: "Gho to Nile",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "17 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
    ],
    progressPct: 50,
    description: (
      <>
        <p>
          Continue along scenic trails through Chekampar and ascend steadily to Nile. Pass chortens, mani walls, and get immersed in the rich Tibetan culture of the region.
        </p>
      </>
    ),
    altitudes: [{ label: "Nile", value: "3,480 m" }],
  },
  {
    day: 7,
    title: "Nile to Mu Gompa and return to Chhekampar",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "16 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
    ],
    progressPct: 58,
    description: (
      <>
        <p>
          Trek to Mu Gompa, the highest monastery in Tsum Valley, offering a tranquil spiritual experience and epic views of Ganesh Himal. Return to Chhekampar for the night.
        </p>
      </>
    ),
    altitudes: [
      { label: "Mu Gompa", value: "3,700 m" },
      { label: "Chhekampar", value: "3,000 m" },
    ],
  },
  {
    day: 8,
    title: "Chhekampar to Chumling",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "15.5 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
    ],
    progressPct: 67,
    description: (
      <>
        <p>
          Descend through pine forests and peaceful trails decorated with prayer flags. The route offers excellent views of Ganesh Himal as you reach Chumling.
        </p>
      </>
    ),
    altitudes: [{ label: "Chumling", value: "2,386 m" }],
  },
  {
    day: 9,
    title: "Chumling to Philim",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "13 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "5 hrs" },
    ],
    progressPct: 75,
    description: (
      <>
        <p>
          Trek along the Syar River, cross bridges, and walk beside steep cliffs as you rejoin the main Manaslu trail near Philim village.
        </p>
      </>
    ),
    altitudes: [{ label: "Philim", value: "1,590 m" }],
  },
  {
    day: 10,
    title: "Philim to Machhakhola",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "23 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs" },
    ],
    progressPct: 83,
    description: (
      <>
        <p>
          Return trek toward Machhakhola through lush landscapes, terraced fields, and traditional villages along the Budhi Gandaki River.
        </p>
      </>
    ),
    altitudes: [{ label: "Machhakhola", value: "870 m" }],
  },
  {
    day: 11,
    title: "Drive from Machhakhola to Kathmandu/Pokhara",
    stats: [
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive Duration", value: "8–10 hrs" },
    ],
    progressPct: 92,
    description: (
      <>
        <p>
          Drive back to either Kathmandu or Pokhara from Machhakhola, marking the end of your adventure through the sacred Tsum Valley.
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu or Pokhara", value: "1,324 m / 822 m" }],
  },
  {
    day: 12,
    title: "Final Departure from Kathmandu",
    stats: [],
    progressPct: 100,
    description: (
      <>
        <p>
          Our team will transfer you to the airport for your departure. Thank you for exploring the Tsum Valley with us!
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,324 m" }],
  },
];
