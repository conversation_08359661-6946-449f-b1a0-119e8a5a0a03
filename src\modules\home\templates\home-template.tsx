import React from 'react';
import HeroSection from '../components/hero-section';
import { TailoredEveryAdventure } from '../components/tailored-every-adventure';
import TestimonialsSection from '../components/testimonials';
// import ExperienceSection from '../components/experience-section';
import ActivityCategories from '../components/category-section';
import HikingAreasSection from '../components/hiking-areas';
import BlogsSection from '../components/blogs-section';
import VideoTestimonial from '../components/video.testimonial';
import UpcomingTreksSection from '../components/upcoming-trek';
import { IHome } from '@/types/home';
import OverviewSection from '../components/overview-section';
import FeaturedPackages from '../components/feature-section';
import getPackages from '@/actions/package/get-packages';
import InstagramFeed from '../components/instagram';
// import OurTeamSection from '@/modules/about/components/team-section';

const HomeTemplate = async ({ home }: { home: IHome }) => {

  const response = await getPackages();
  const allPackages = response.data || [];
  const tripPackages = allPackages.filter(pkg => pkg.tripOftheMonth);

    // console.log("Home object:", home)

  // console.log("Featured packages:", home.featuredpackages)

  return (
    <div>
      <HeroSection hero={home.hero} />
      <OverviewSection overview={home.overview} />
      <ActivityCategories adventure={home.adventure} />
      {/* <ExperienceSection experience={home.experience} /> */}
      <HikingAreasSection hiking={home.hiking} />
      <FeaturedPackages  featuredpackages={tripPackages} />
      <UpcomingTreksSection />
      <TailoredEveryAdventure adventure={home.tailoredAdventure} />
      <BlogsSection />
      {/* <OurTeamSection/> */}
      <InstagramFeed />
      <VideoTestimonial testimonial={home.videoTestimonial} />
      <TestimonialsSection testimonial={home.review} />
    </div>
  );
};

export default HomeTemplate;
