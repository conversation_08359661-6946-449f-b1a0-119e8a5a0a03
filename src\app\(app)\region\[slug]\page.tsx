import { notFound } from "next/navigation";
import type { Metada<PERSON> } from "next";
import type { IPackage } from "@/types/package";
import { getPackagesByRegionSlugWithPagination } from "@/actions/package/get-packages-by-region";
import PackageCard from "@/components/common/cards/card";
import Link from "next/link";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight } from "lucide-react";

type Params = { slug: string };

export const revalidate = 0;

export async function generateMetadata(
    { params }: { params: Promise<Params> }
): Promise<Metadata> {
    const { slug } = await params;
    return {
        title: `${slug.replace(/-/g, " ")} | Packages by Region`,
        description: `Explore trekking and travel packages available in the ${slug.replace(/-/g, " ")} region, grouped by activity.`,
    };
}

export default async function RegionPage(
    { params }: { params: Promise<Params> }
) {
    const { slug } = await params;

    const res = await getPackagesByRegionSlugWithPagination(slug);
    if (!res?.success) notFound();

    const packages: IPackage[] = res.data ?? [];
    if (packages.length === 0) {
        return (
            <section className="container mx-auto px-4 py-12">
                <h1 className="text-3xl md:text-4xl font-bold mb-2 capitalize">
                    {slug.replace(/-/g, " ")}
                </h1>
                <p className="text-muted-foreground">
                    No packages found in this region yet. Please check back soon.
                </p>
            </section>
        );
    }

    const groups = packages.reduce((map, pkg) => {
        const key = pkg.activity?.slug ?? "other";
        if (!map.has(key)) {
            map.set(key, {
                title: pkg.activity?.name ?? "Other",
                activitySlug: pkg.activity?.slug,
                items: [] as IPackage[],
            });
        }
        map.get(key)!.items.push(pkg);
        return map;
    }, new Map<string, { title: string; activitySlug?: string; items: IPackage[] }>());

    return (
        <section className="container mx-auto px-4 py-10">
            <header className="mb-8">
                <h1 className="text-3xl text-brand md:text-4xl font-bold capitalize">
                    {slug.replace(/-/g, " ")}
                </h1>
            </header>

            <div className="space-y-12">
                {[...groups.entries()].map(([key, group]) => (
                    <section key={key}>
                        <div className="flex items-end justify-between mb-5">
                            <h2 className="text-2xl md:text-3xl font-semibold">
                                {group.title}
                            </h2>
                            {group.activitySlug ? (
                                <Link
                                    href={`/${group.activitySlug}`}
                                    className="text-sm text-primary hover:underline"
                                >
                                    View all
                                </Link>
                            ) : null}
                        </div>

                        <Carousel className="w-full">
                            <CarouselContent className="-ml-4">
                                {group.items.map((pkg) => (
                                    <CarouselItem
                                        key={pkg.id}
                                        className="pl-4 basis-full sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/3"
                                    >
                                        <Link href={`/${pkg.activity.slug}/${pkg.slug}`} className="block">
                                            <PackageCard package={pkg} />
                                        </Link>
                                    </CarouselItem>
                                ))}
                            </CarouselContent>

                            <CarouselPrevious className="-left-4 md:-left-6 bg-white border border-gray-200 hover:bg-gray-50">
                                <ChevronLeft className="h-5 w-5" />
                            </CarouselPrevious>
                            <CarouselNext className="-right-4 md:-right-6 bg-white border border-gray-200 hover:bg-gray-50">
                                <ChevronRight className="h-5 w-5" />
                            </CarouselNext>
                        </Carousel>
                    </section>
                ))}
            </div>
        </section>
    );
}
