'use client';

import { IHomeExperience } from '@/types/home';
import { motion } from 'framer-motion';

const features = [
  {
    title: "Explore Places You Couldn't Yourself",
    desc: 'All trips are led by certified expert guides, unlocking life experiences in places most never see.',
    icon: (
      <svg
        viewBox="0 0 48 48"
        className="h-16 w-16 stroke-current"
        fill="none"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M24 3 10 9v12c0 10 6 18 14 24 8-6 14-14 14-24V9L24 3Z" />
        <path d="m18 22 4 4 8-8" />
      </svg>
    ),
  },
  {
    title: 'Join a Small Like‑Minded Group',
    desc: '75% join our trips as solo travellers, with most in their 30s–50s. 95% give our group dynamic 5 stars.',
    icon: (
      <svg
        viewBox="0 0 48 48"
        className="h-16 w-16 stroke-current"
        fill="none"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="4" y="14" width="16" height="20" rx="3" />
        <rect x="28" y="14" width="16" height="20" rx="3" />
        <path d="M20 24h8" />
      </svg>
    ),
  },
  {
    title: 'Hassle‑Free From Start to Finish',
    desc: 'We’ve sorted the logistics, so you can just rock up and have a blast in the wild.',
    icon: (
      <svg
        viewBox="0 0 48 48"
        className="h-16 w-16 stroke-current"
        fill="none"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <circle cx="24" cy="12" r="5" />
        <path d="M18 22h12l4 20H14l4-20Z" />
        <path d="M14 40h20" />
      </svg>
    ),
  },
];

export default function ExperienceSection({
  experience,
}: {
  experience: IHomeExperience;
}) {
  return (
    <section className="relative isolate py-8 md:py-16">
      <div className="container mx-auto px-4 pb-0">
        <motion.div
          initial={{ opacity: 0, y: 24 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.4 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          className="container mx-auto text-center"
        >
          {/* <span className="block text-xs font-medium tracking-wider uppercase text-gray-600 mb-4">
            Explore
          </span> */}
          <h2 className="md:text-4xl text-3xl font-bold text-dark leading-tight tracking-tight">
            {experience.heading}
          </h2>
          <p className="mt-4 text-sm md:text-base text-gray-60 mb-10">
            {experience.subHeading}
          </p>
        </motion.div>

        {/* Features */}
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-3">
            {experience.features.map((f, i) => (
              <motion.div
                key={f.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.55,
                  ease: 'easeOut',
                  delay: i * 0.12,
                }}
                className="flex flex-col items-center text-center px-6 py-10 relative"
              >
                {/* Vertical separators */}
                {i !== 0 && (
                  <span className="hidden md:block absolute left-0 top-8 bottom-8 w-px bg-gray-200" />
                )}
                {i !== features.length - 1 && (
                  <span className="hidden md:block absolute right-0 top-8 bottom-8 w-px bg-gray-200" />
                )}

                {/* <div className="text-gray-800 mb-4">{f.icon}</div> */}

                <h3 className="text-base line-clamp-2 tracking-wider font-extrabold uppercase leading-snug text-gray-900 ">
                  {f.title}
                </h3>
                <p className="mt-4 text-sm leading-relaxed text-gray-600 max-w-xs">
                  {f.subtitle}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Jagged / Mountain bottom edge */}
          <div className="absolute left-0 right-0 -bottom-[55px] pointer-events-none select-none">
            <svg
              className="w-full h-[60px] text-brand"
              preserveAspectRatio="none"
              viewBox="0 0 1440 120"
            >
              <path
                fill="currentColor"
                d="M0 80L60 70C120 60 240 40 360 48C480 56 600 92 720 101.3C840 111 960 93 1080 72C1200 51 1320 27 1380 16L1440 5V121H1380C1320 121 1200 121 1080 121C960 121 840 121 720 121C600 121 480 121 360 121C240 121 120 121 60 121H0Z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Spacer to account for jagged edge overlap */}
      <div className="h-20" />
    </section>
  );
}
