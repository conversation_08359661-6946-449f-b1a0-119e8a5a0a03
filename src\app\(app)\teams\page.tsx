import React from 'react'
import getTeams from '@/actions/teams/get-all-teams';
import HtmlContentDisplay from '@/components/html-content-display';

export default async function OurTeamPage() {
    const teamsResult = await getTeams();
    const team = teamsResult?.data || [];

    return (
        <>
        
            <div className="container mx-auto py-8">
                <h2 className="text-2xl font-bold mb-8">{team.title}</h2>
                {team.members?.map(member => (
                    <div key={member.id} className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12 items-start">
                        <div className="md:col-span-1">
                            <img
                                src={member.image}
                                alt={member.name}
                                className="rounded-lg shadow w-full h-auto object-cover mb-4"
                            />
                            <div className="text-lg font-semibold">{member.name}</div>
                            <div className="text-sm text-gray-600">{member.role}</div>
                        </div>
                        <div className="md:col-span-3 flex flex-col justify-center">
                            <div className="text-base text-gray-800 whitespace-pre-line">
                                <HtmlContentDisplay
                                    htmlContent={member.bio}
                                    className="text-justify"
                                />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </>
    )
}
