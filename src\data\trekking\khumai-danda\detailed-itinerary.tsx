import React from "react";
import { Navigation, Clock } from "lucide-react";
import { DayDetail } from "@/modules/fastpacking/components/itinerary-detailed";

export const detailedKhumaiDandaItinerary: DayDetail[] = [
  {
    day: 1,
    title: "Arrival in Kathmandu",
    stats: [],
    progressPct: 12,
    description: (
      <>
        <p>
          After your arrival at Kathmandu, one of our representatives will receive you at the airport and transfer you to your hotel. Take the evening to rest and prepare for the trek ahead.
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,400 m" }],
  },
  {
    day: 2,
    title: "Kathmandu → Pokhara",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive/Flight Duration", value: "6–7 hrs / 25 min flight" },
    ],
    progressPct: 24,
    description: (
      <>
        <p>
          Today, you’ll either take a scenic drive or a short flight to Pokhara. After checking into your hotel, enjoy a peaceful evening by the lakeside.
        </p>
      </>
    ),
    altitudes: [
      { label: "Kathmandu", value: "1,400 m" },
      { label: "Pokhara", value: "822 m" },
    ],
  },
  {
    day: 3,
    title: "Drive to Lumre → Trek to Kaltha",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Trek Distance", value: "6 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "3–4 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "1,068 m" },
    ],
    progressPct: 36,
    description: (
      <>
        <p>
          After a 40–50 minute drive to Lumre, begin your trek through stone steps and lush forests. The trail leads uphill to Kaltha, offering serene views and peaceful surroundings. Overnight at Kaltha.
        </p>
      </>
    ),
    altitudes: [
      { label: "Lumre", value: "1,200 m" },
      { label: "Kaltha", value: "1,890 m" },
    ],
  },
  {
    day: 4,
    title: "Kaltha to Meshroom",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "7 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "5 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "1,010 m" },
    ],
    progressPct: 48,
    description: (
      <>
        <p>
          The trail from Kaltha to Meshroom winds through dense rhododendron and pine forests. With every ascent, you gain stunning views of Machhapuchhare and the Annapurna range. Overnight at Meshroom.
        </p>
      </>
    ),
    altitudes: [
      { label: "Kaltha", value: "1,890 m" },
      { label: "Meshroom", value: "2,900 m" },
    ],
  },
  {
    day: 5,
    title: "Meshroom → Korchan Danda → Khumai Danda",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "10 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "7–8 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Gain", value: "345 m" },
    ],
    progressPct: 60,
    description: (
      <>
        <p>
          Ascend to Korchan Danda (3,682 m), the highest point of the trek, where you’ll enjoy panoramic views of Annapurna, Mardi Himal, and Machhapuchhare. Then descend to Khumai Danda for an overnight stay on a scenic ridge.
        </p>
      </>
    ),
    altitudes: [
      { label: "Korchan Danda", value: "3,682 m" },
      { label: "Khumai Danda", value: "3,245 m" },
    ],
  },
  {
    day: 6,
    title: "Khumai Danda → Ghachowk → Drive to Pokhara",
    stats: [
      { icon: <Clock size={16} className="text-dark/80" />, label: "Trek Duration", value: "5–6 hrs" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Drive Duration", value: "2 hrs" },
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Elevation Loss", value: "2,423 m" },
    ],
    progressPct: 72,
    description: (
      <>
        <p>
          Descend through rhododendron forests and peaceful villages to Ghachowk. After the trek, drive back to Pokhara and enjoy the evening in the lakeside town.
        </p>
      </>
    ),
    altitudes: [
      { label: "Khumai Danda", value: "3,245 m" },
      { label: "Ghachowk", value: "1,000 m approx." },
      { label: "Pokhara", value: "822 m" },
    ],
  },
  {
    day: 7,
    title: "Pokhara → Kathmandu",
    stats: [
      { icon: <Navigation size={16} className="text-dark/80" />, label: "Distance", value: "200 km" },
      { icon: <Clock size={16} className="text-dark/80" />, label: "Duration", value: "6–7 hrs / 25 min flight" },
    ],
    progressPct: 84,
    description: (
      <>
        <p>
          After breakfast, return to Kathmandu by road or flight. The rest of the day is free for exploration or relaxation.
        </p>
      </>
    ),
    altitudes: [
      { label: "Pokhara", value: "822 m" },
      { label: "Kathmandu", value: "1,400 m" },
    ],
  },
  {
    day: 8,
    title: "Final Departure from Kathmandu",
    stats: [],
    progressPct: 100,
    description: (
      <>
        <p>
          Our representative will drop you at the airport as per your flight schedule. Thank you for trekking with us and we hope to see you again in Nepal!
        </p>
      </>
    ),
    altitudes: [{ label: "Kathmandu", value: "1,400 m" }],
  },
];
