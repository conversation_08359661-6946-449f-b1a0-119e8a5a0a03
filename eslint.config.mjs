import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  {
    rules: {
      // Disable unused variables
      "@typescript-eslint/no-unused-vars": "off",

      // Allow `any` type usage
      "@typescript-eslint/no-explicit-any": "off",

      // Disable requirement for explicit function return types
      "@typescript-eslint/explicit-function-return-type": "off",

      // Disable banning of `@ts-ignore` and similar comments
      "@typescript-eslint/ban-ts-comment": "off",

      // (Optional) Disable strict type bans like `{}`
      "@typescript-eslint/ban-types": "off",
    },
  },
];

export default eslintConfig;
