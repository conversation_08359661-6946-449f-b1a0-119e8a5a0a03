import React from 'react';
import { notFound } from 'next/navigation';
import BlogDetail from '@/modules/blogs/templates/blog-detail';
import getBlogbySlug from '@/actions/blogs/get-blog-by-slug';
import { IBlog } from '@/types/blogs';
import getBlogs from '@/actions/blogs/get-all-blogs';


type Params = { slug: string };

export default async function BlogPage(
    props: { params: Promise<Params> }
) {
    const { slug } = await props.params;

    const data = await getBlogbySlug(slug);

    if (!data?.success || !data.data) {
        notFound();
    }

    const post: IBlog = data.data;
    const related = (await getBlogs()).data;

    return <BlogDetail blog={post} related={related} />;
};

