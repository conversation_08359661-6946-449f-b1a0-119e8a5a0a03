'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { IHomeReview } from '@/types/home';

interface Testimonial {
  id: number;
  name: string;
  trek: string;
  date: string;
  quote: string;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    trek: 'Kedarkantha Trek',
    date: 'Jan 2024',
    quote:
      "A really random quote found on the internet. Yes I'm not really creative. And enclosing it in double quotes cause aesthetics.",
    image: '/images/testimonial/testimonial1.jpg',
  },
  {
    id: 2,
    name: '<PERSON>',
    trek: 'Annapurna Base Camp',
    date: 'Mar 2024',
    quote:
      'Guides felt like lifelong friends. Logistics: flawless. Scenery: unreal. Would book again tomorrow.',
    image: '/images/testimonial/testimonial2.jpg',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    trek: 'Everest Panorama',
    date: 'Oct 2023',
    quote:
      "Professional pacing and constant safety checks let me enjoy every step. Best multi‑day trek experience I've had.",
    image: '/images/testimonial/testimonial3.jpg',
  },
  {
    id: 4,
    name: 'Sofia Martinez',
    trek: 'Mardi Himal',
    date: 'Dec 2023',
    quote:
      'The itinerary design balanced challenge & rest perfectly. Photo spots? They knew every hidden angle.',
    image: '/images/testimonial/testimonial4.jpg',
  },
  {
    id: 5,
    name: 'Rando M Guy',
    trek: 'Kedarkantha Trek',
    date: 'Jan 2024',
    quote:
      "A really random quote found on the internet. Yes I'm not really creative. And enclosing it in double quotes cause aesthetics.",
    image: '/images/testimonial/testimonial1.jpg',
  },
  {
    id: 6,
    name: 'Emily Walker',
    trek: 'Annapurna Base Camp',
    date: 'Mar 2024',
    quote:
      'Guides felt like lifelong friends. Logistics: flawless. Scenery: unreal. Would book again tomorrow.',
    image: '/images/testimonial/testimonial2.jpg',
  },
  {
    id: 7,
    name: 'Karan Patel',
    trek: 'Everest Panorama',
    date: 'Oct 2023',
    quote:
      "Professional pacing and constant safety checks let me enjoy every step. Best multi‑day trek experience I've had.",
    image: '/images/testimonial/testimonial3.jpg',
  },
  {
    id: 8,
    name: 'Sofia Martinez',
    trek: 'Mardi Himal',
    date: 'Dec 2023',
    quote:
      'The itinerary design balanced challenge & rest perfectly. Photo spots? They knew every hidden angle.',
    image: '/images/testimonial/testimonial4.jpg',
  },
];

/** CONFIG */
const AUTO_INTERVAL = 4500; // ms
const STACK_SIZE = 4;
const DRAG_THRESHOLD = 80; // px

export default function TestimonialsSection({
  testimonial,
}: {
  testimonial: IHomeReview;
}) {
  const [index, setIndex] = useState(0);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const paused = useRef(false);

  const next = useCallback(
    () => setIndex((i) => (i + 1) % testimonials.length),
    []
  );
  const prev = useCallback(
    () => setIndex((i) => (i - 1 + testimonials.length) % testimonials.length),
    []
  );

  const clearTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  const startTimer = useCallback(() => {
    clearTimer();
    if (!paused.current) {
      timerRef.current = setTimeout(next, AUTO_INTERVAL);
    }
  }, [next]);

  // Autoplay
  useEffect(() => {
    startTimer();
    return clearTimer;
  }, [index, startTimer]);

  // Pause when tab hidden
  useEffect(() => {
    const onVis = () => {
      paused.current = document.hidden;
      startTimer();
    };
    document.addEventListener('visibilitychange', onVis);
    return () => document.removeEventListener('visibilitychange', onVis);
  }, [startTimer]);

  // Keyboard arrows
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        prev();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        next();
      }
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [next, prev]);

  const handlePointerEnter = () => {
    paused.current = true;
    clearTimer();
  };
  const handlePointerLeave = () => {
    paused.current = false;
    startTimer();
  };

  // Visible stacked cards
  const visible = Array.from(
    { length: Math.min(STACK_SIZE, testimonial.reviews.length) },
    (_unused, layer) => {
      const i = (index + layer) % testimonial.reviews.length;
      return { layer, item: testimonial.reviews[i] };
    }
  );

  return (
    <section className="relative container mx-auto px-4 mb-24 md:mb-16">
      <div className="grid md:grid-cols-2 gap-10 items-center">
        {/* Left */}
        <div className="space-y-6">
          <h2 className="md:text-4xl text-3xl font-bold text-dark leading-tight">
            {testimonial.title}
          </h2>
          <p className="text-gray-600 text-base md:text-lg space-y-4">
            {testimonial.subtitle}
          </p>

          <Link href="/reviews">
            <Button className="text-white">See all reviews</Button>
          </Link>
        </div>

        {/* Right / Carousel */}
        <div
          className="relative h-[420px] select-none group"
          onPointerEnter={handlePointerEnter}
          onPointerLeave={handlePointerLeave}
          aria-roledescription="carousel"
        >
          {/* Side Chevron Controls */}
          <button
            type="button"
            onClick={prev}
            aria-label="Previous testimonial"
            className="
              absolute top-1/2 -translate-y-1/2 -left-4 md:-left-20
              inline-flex items-center justify-center
              h-10 w-10 rounded-full
              bg-white/70 backdrop-blur border border-black/10
              shadow hover:bg-white focus-visible:outline-2 focus-visible:outline-black
              opacity-0 md:opacity-0 group-hover:opacity-100 transition
            "
          >
            <ChevronLeft className="h-5 w-5 text-black" />
          </button>

          <button
            type="button"
            onClick={next}
            aria-label="Next testimonial"
            className="
              absolute top-1/2 -translate-y-1/2 -right-4 md:-right-8
              inline-flex items-center justify-center
              h-10 w-10 rounded-full
              bg-white/70 backdrop-blur border border-black/10
              shadow hover:bg-white  focus-visible:outline-2 focus-visible:outline-black
              opacity-0 md:opacity-0 group-hover:opacity-100 transition
            "
          >
            <ChevronRight className="h-5 w-5 text-black" />
          </button>

          {/* OPTIONAL side fades (uncomment if desired)
          <div className="pointer-events-none absolute inset-y-0 left-0 w-10 bg-gradient-to-r from-white/60 via-white/0 to-transparent" />
          <div className="pointer-events-none absolute inset-y-0 right-0 w-10 bg-gradient-to-l from-white/60 via-white/0 to-transparent" />
          */}

          {visible.map(({ layer, item }) => {
            const depth = layer;
            const total = visible.length;
            const scale = 1 - depth * 0.05;
            const translateX = depth * 42;
            const translateY = depth * 8;
            const blur =
              depth === 0
                ? 'blur-0'
                : depth === total - 1
                ? 'blur-[2px]'
                : 'blur-[1px]';
            const z = total - depth;

            return (
              <AnimatePresence key={item.id}>
                <motion.div
                  layout
                  drag={depth === 0 ? 'x' : false}
                  dragConstraints={{ left: 0, right: 0 }}
                  dragElastic={0.6}
                  onDragEnd={(_e, info) => {
                    if (depth !== 0) return;
                    if (info.offset.x < -DRAG_THRESHOLD) next();
                    else if (info.offset.x > DRAG_THRESHOLD) prev();
                  }}
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  animate={{
                    opacity: 1,
                    scale,
                    x: translateX,
                    y: translateY,
                  }}
                  exit={{ opacity: 0, scale: 0.9, y: -10 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  className={[
                    'absolute top-0 left-0 w-[260px] md:w-[440px] h-[420px] rounded-3xl overflow-hidden shadow-xl cursor-grab active:cursor-grabbing origin-center bg-black/5',
                    'ring-1 ring-black/5 backdrop-blur-sm',
                    blur,
                    'group/card',
                    depth === 0 ? 'pointer-events-auto' : 'pointer-events-none',
                  ].join(' ')}
                  style={{ zIndex: z }}
                  aria-hidden={depth !== 0}
                >
                  <Image
                    src={item.image}
                    alt={`Photo of ${item.name}`}
                    fill
                    className="object-cover"
                    priority={depth < 2}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-black/0" />
                  <div className="absolute inset-x-0 bottom-0 p-5 text-white space-y-3">
                    <blockquote className="text-sm leading-relaxed">
                      “{item.quote}”
                    </blockquote>
                    <div className="text-xs font-medium tracking-wide opacity-90">
                      — {item.name}
                      <div className="opacity-70 font-normal">
                        {item.designation},{' '}
                        {new Date(item.date).toLocaleString('en-US', {
                          day: 'numeric',
                          month: 'short',
                          year: 'numeric',
                        })}
                      </div>
                    </div>
                  </div>
                  {depth === 0 && (
                    <div className="absolute inset-0 rounded-3xl transition-all duration-300 group-hover/card:shadow-2xl group-hover/card:-translate-y-1" />
                  )}
                </motion.div>
              </AnimatePresence>
            );
          })}

          <div className="absolute -bottom-10 flex gap-2">
            {testimonials.map((t, i) => {
              const active = i === index;
              return (
                <button
                  key={t.id}
                  aria-label={`Show testimonial ${i + 1}`}
                  onClick={() => setIndex(i)}
                  className={`h-2.5 rounded-full transition-all ${
                    active
                      ? 'w-6 bg-black'
                      : 'w-2.5 bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              );
            })}
          </div>

          {/* (If you wanted the old bottom chevron buttons, re-add them here) */}
        </div>
      </div>
    </section>
  );
}
