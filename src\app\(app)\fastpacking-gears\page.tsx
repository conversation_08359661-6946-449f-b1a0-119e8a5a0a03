// "use client"

// import React, { useState } from 'react';
// import { Card, CardContent } from '@/components/ui/card';
// import { Badge } from '@/components/ui/badge';
// import Image from 'next/image';
// import Divider from '@/modules/home/<USER>/divider';

// const gearItems = [
//   {
//     image: "/images/gears/bagpack.webp",
//     title: "Backpack",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "Zpacks Nero Ultra 38 is one of the perfect the perfect vessel for carrying a super ultralight kit. Constructed with waterproof and tough-as-nails Challenge Ultra fabric, the base pack is frameless, hipbelt-less, and weighs only 10.2 oz when fully stripped down. Yet it is still very comfortable and well-featured with 2.75″ wide shoulder straps, a foam sit pad back panel, and a full-sized set of side and rear external pockets. This fastpacking pack has proven very effective for us in managing base weights under ten pounds."
//   },
//   {
//     image: "/images/gears/sleeping-bag.webp",
//     title: "Sleeping Bag/Pad",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "The Therm-a-Rest NeoAir XTherm NXT is an absolute beast of a sleeping pad. It has a remarkable 7.3 R-value while still weighing only a pound and packing down to a very small size. We can’t find a single comparable pad on the market that provides this much warmth and comfort in such a lightweight package. You should definately consider this as your fastpacking gear option."
//   },
//   {
//     image: "/images/gears/jacket.webp",
//     title: "Insulated Jacket",
//     category: "Warmth",
//     weight: "10.2 oz",
//     description: "Rab Microlight could be your perfect fastpacking jacket. It is very light, easy to fit and will keep you warm."
//   },
//   {
//     image: "/images/gears/sweatpants.webp",
//     title: "Sweatpants",
//     category: "Clothing",
//     weight: "10.2 oz",
//     description: "Find yourself a comfortable and light sweatpants that is easy to fit in the bag and helps you jog comfortably."
//   },
//   {
//     image: "/images/gears/socks.webp",
//     title: "Fastpacking Socks",
//     category: "Footwear",
//     weight: "10.2 oz",
//     description: "Buy comfortable pairs of socks that will help you jog in difficult terrain. Paka performance socks is very widely used in fastpacking trekks."
//   },
//   {
//     image: "/images/gears/hiking.webp",
//     title: "Fastpacking Boots",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "There are a lot of trekking shoes/boots available in the market. Make sure to choose the comfortable boot that will help you run faster and better distance in short amount of time."
//   },
//   {
//     image: "/images/gears/jacket.webp",
//     title: "Fastpacking Harness",
//     category: "Warmth",
//     weight: "10.2 oz",
//     description: "Raincoat is very important equipement to carry while you are going on a trek. You never know when its going to rain. Choose a raincoat that is very light and wont take much space in your bag."
//   },
//   {
//     image: "/images/gears/shorts.webp",
//     title: "Fastpacking Shorts",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "Carry a pair of shorts, it wont take much space in your bag. It will help you jog faster and reach your destination faster. Shorts are crucial fastpacking equipment."
//   },
//   {
//     image: "/images/gears/first-aid-kit.webp",
//     title: "First-Aid Kit",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "It is very important to carry first aid bag when you are on your treking journey. Carry all the basic medicines, bandages, scissors, etc. First aid kit will always come in handy for basic treatment."
//   },
//   {
//     image: "/images/gears/compass.webp",
//     title: "Navigation Tools",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "A geographical compass will help you determine direction by indicating north, enabling navigation with a map to reach a specific destination. Compass are small in sizes and easier to carry."
//   },
//   {
//     image: "/images/gears/water-bottle.webp",
//     title: "Water Bottle",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "Carry a water bottle/thermos that will  keep the water warm at cold temperature. Do not use carry plastic bottles, keeping hot water in plastic bottle is harmful."
//   },
//   {
//     image: "/images/gears/toiletries.webp",
//     title: "Personal Hygiene Kit",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "Carry your own personal hygiene kit that includes soap, toilet papers, lotions, towels etc. You might not find proper hygienic equipments in at your accommodation."
//   },
//   {
//     image: "/images/gears/powerbank.webp",
//     title: "Powerbank",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "It is very important to carry powerbanks to charge your mobile phones. When you are at remote area/high alittude in nepal, there might not be electricity or solar to charge your electronic gadgets. So, keeping powerbackups will help your device stay healthy."
//   },
//   {
//     image: "/images/gears/slippers.webp",
//     title: "Slippers",
//     category: "Essential",
//     weight: "10.2 oz",
//     description: "Please carry a slipper with you as it will help you explore areas when you are at tea house. It wil also be handy for going to restrooms etc."
//   }
// ];

// interface ExpandedItems {
//   [key: number]: boolean;
// }

// const FastpackingGuide = () => {
//   const [expandedItems, setExpandedItems] = useState<ExpandedItems>({});

//   const toggleExpanded = (index: number) => {
//     setExpandedItems(prev => ({
//       ...prev,
//       [index]: !prev[index]
//     }));
//   }

//   const truncateText = (text: string, maxLength = 150) => {
//     if (text.length <= maxLength) return text;
//     return text.substring(0, maxLength) + '...';
//   };

//   return (
//     <div className="min-h-screen">
//       <div className="relative md:h-screen h-[500px] min-h-[300px] overflow-hidden">
//         <div className="absolute inset-0">
//           <div className="relative w-full h-full">
//             <div className="absolute inset-0 transition-opacity duration-1000 ease-in-out">
//               <div className='relative w-full h-full'>
//                 <Image
//                   src="/images/footer-image.png"
//                   alt="Annapurna Circuit Fastpacking"
//                   fill
//                   className="w-full h-full object-cover"
//                   priority
//                 />
//               </div>
//             </div>
//           </div>
//         </div>

//         <div className="absolute inset-0 bg-black/10"></div>
//         <Divider />

//         <div className="relative z-10 flex h-full items-end justify-start pb-16 md:pb-24">
//           <div className="container mx-auto px-4 text-left">
//             <h1 className="text-5xl md:text-6xl font-bold text-light mb-10">
//               Fastpacking Gears and Equipments
//             </h1>
//           </div>
//         </div>
//       </div>

//       <div className="bg-background">
//         <div className="container mx-auto px-2 md:px-4 py-16">
//           <div className="container mx-auto mb-16">
//             <div className="text-center mb-12">
//               <h2 className="text-3xl md:text-4xl font-bold text-brand mb-6">
//                 The Art of Fastpacking
//               </h2>
//               <div className="w-24 h-1 bg-gradient-to-r from-brand to-accent mx-auto mb-8"></div>
//             </div>

//             <Card className="p-8 mb-8 border-0 shadow-lg bg-gradient-to-br from-white to-secondary/30">
//               <CardContent className="p-0">
//                 <p className="text-lg text-foreground/80 leading-relaxed mb-6">
//                   The main concept of fastpacking is to carry a lighter bag that helps you travel further distances in less time. Fastpacking gear enables trekkers to run and walk to cover multiple days of terrain efficiently.
//                 </p>
//                 <p className="text-lg text-foreground/80 leading-relaxed">
//                   By fitting all necessary supplies into a lightweight backpack, you can travel faster and cover greater distances, enabling you to explore more without needing to return to a fixed base each night.
//                 </p>
//               </CardContent>
//             </Card>

//             <Card className="p-8 border-0 shadow-lg bg-gradient-to-br from-accent/5 to-brand/5">
//               <CardContent className="p-0">
//                 <h3 className="text-2xl font-bold text-brand mb-4">Pack Like a Pro</h3>
//                 <p className="text-lg text-foreground/80 leading-relaxed">
//                   When fastpacking, keeping your gear well-organized is essential. Being aware of what you have and where it&apos;s stored, along with packing efficiently, simplifies everything. Use clear, waterproof zip-lock bags for equipment and dry bags for clothes and sleeping gear.
//                 </p>
//               </CardContent>
//             </Card>
//           </div>

//           <div className="container mx-auto">
//             <div className="text-center mb-12">
//               <h2 className="text-3xl md:text-4xl font-bold text-brand mb-4">
//                 Essential Fastpacking Gear
//               </h2>
//               <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
//                 Carefully curated equipment to maximize your adventure while minimizing weight
//               </p>
//             </div>

//             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
//               {gearItems.map((item, index) => (
//                 <Card
//                   key={index}
//                   className="group cursor-pointer border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-b from-white to-secondary/10"
//                 >
//                   <CardContent className="p-0">
//                     <div className="relative overflow-hidden rounded-t-xl">
//                       <Image
//                         src={item.image}
//                         alt={item.title}
//                         width={200}
//                         height={300}
//                         className="w-full h-64 object-fit group-hover:scale-105 transition-transform duration-500"
//                       />
//                       <div className="absolute top-4 left-4 flex gap-2">
//                         <Badge className="bg-brand text-white">
//                           {item.category}
//                         </Badge>
//                         <Badge variant="secondary" className="bg-white/90 text-foreground">
//                           {item.weight}
//                         </Badge>
//                       </div>
//                     </div>

//                     <div className="p-6">
//                       <h3 className="text-xl font-bold text-brand mb-3 group-hover:text-accent transition-colors">
//                         {item.title}
//                       </h3>
//                       <div className="text-gray-600 leading-relaxed">
//                         <p className="mb-2">
//                           {expandedItems[index] ? item.description : truncateText(item.description)}
//                         </p>
//                         {item.description.length > 150 && (
//                           <button
//                             onClick={() => toggleExpanded(index)}
//                             className="text-brand hover:text-secondary font-medium text-sm transition-colors underline"
//                           >
//                             {expandedItems[index] ? 'Read Less' : 'Read More'}
//                           </button>
//                         )}
//                       </div>
//                     </div>
//                   </CardContent>
//                 </Card>
//               ))}
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default FastpackingGuide;

import React from 'react';
import Image from 'next/image';
import Divider from '@/modules/home/<USER>/divider';
import { Card, CardContent } from '@/components/ui/card';

const FastpackingGuide = () => {
  const gearItems = [
    {
      image: "/images/gears/bagpack.webp",
      title: "Backpack",
      description: "Zpacks Nero Ultra 38 is one of the perfect the perfect vessel for carrying a super ultralight kit. Constructed with waterproof and tough-as-nails Challenge Ultra fabric, the base pack is frameless, hipbelt-less, and weighs only 10.2 oz when fully stripped down. Yet it is still very comfortable and well-featured with 2.75″ wide shoulder straps, a foam sit pad back panel, and a full-sized set of side and rear external pockets. This fastpacking pack has proven very effective for us in managing base weights under ten pounds."
    },
    {
      image: "/images/gears/sleeping-bag.webp",
      title: "Sleeping Bag/Pad",
      description: "The Therm-a-Rest NeoAir XTherm NXT is an absolute beast of a sleeping pad. It has a remarkable 7.3 R-value while still weighing only a pound and packing down to a very small size. We can’t find a single comparable pad on the market that provides this much warmth and comfort in such a lightweight package. You should definately consider this as your fastpacking gear option."
    },
    {
      image: "/images/gears/jacket.webp",
      title: "Insulated Jacket",
      description: "Rab Microlight could be your perfect fastpacking jacket. It is very light, easy to fit and will keep you warm."
    },
    {
      image: "/images/gears/sweatpants.webp",
      title: "Sweatpants",
      description: "Find yourself a comfortable and light sweatpants that is easy to fit in the bag and helps you jog comfortably."
    },
    {
      image: "/images/gears/socks.webp",
      title: "Fastpacking Socks",
      description: "Buy comfortable pairs of socks that will help you jog in difficult terrain. Paka performance socks is very widely used in fastpacking trekks."
    },
    {
      image: "/images/gears/hiking.webp",
      title: "Fastpacking Boots",
      description: "There are a lot of trekking shoes/boots available in the market. Make sure to choose the comfortable boot that will help you run faster and better distance in short amount of time."
    },
    {
      image: "/images/gears/jacket.webp",
      title: "Fastpacking Harness",
      description: "Raincoat is very important equipement to carry while you are going on a trek. You never know when its going to rain. Choose a raincoat that is very light and wont take much space in your bag."
    },
    {
      image: "/images/gears/shorts.webp",
      title: "Fastpacking Shorts",
      description: "Carry a pair of shorts, it wont take much space in your bag. It will help you jog faster and reach your destination faster. Shorts are crucial fastpacking equipment."
    },
    {
      image: "/images/gears/first-aid-kit.webp",
      title: "First-Aid Kit",
      description: "It is very important to carry first aid bag when you are on your treking journey. Carry all the basic medicines, bandages, scissors, etc. First aid kit will always come in handy for basic treatment."
    },
    {
      image: "/images/gears/compass.webp",
      title: "Navigation Tools",
      description: "A geographical compass will help you determine direction by indicating north, enabling navigation with a map to reach a specific destination. Compass are small in sizes and easier to carry."
    },
    {
      image: "/images/gears/water-bottle.webp",
      title: "Water Bottle",
      description: "Carry a water bottle/thermos that will  keep the water warm at cold temperature. Do not use carry plastic bottles, keeping hot water in plastic bottle is harmful."
    },
    {
      image: "/images/gears/toiletries.webp",
      title: "Personal Hygiene Kit",
      description: "Carry your own personal hygiene kit that includes soap, toilet papers, lotions, towels etc. You might not find proper hygienic equipments in at your accommodation."
    },
    {
      image: "/images/gears/powerbank.webp",
      title: "Powerbank",
      description: "It is very important to carry powerbanks to charge your mobile phones. When you are at remote area/high alittude in nepal, there might not be electricity or solar to charge your electronic gadgets. So, keeping powerbackups will help your device stay healthy."
    },
    {
      image: "/images/gears/slippers.webp",
      title: "Slippers",
      description: "Please carry a slipper with you as it will help you explore areas when you are at tea house. It wil also be handy for going to restrooms etc."
    }
  ];

  return (
    <div className="min-h-screen">
      <div className="relative md:h-screen h-[500px] min-h-[300px] overflow-hidden">
        <div className="absolute inset-0">
          <div className="relative w-full h-full">
            <div className="absolute inset-0 transition-opacity duration-1000 ease-in-out">
              <div className='relative w-full h-full'>
                <Image
                  src="/images/footer-image.png"
                  alt="Annapurna Circuit Fastpacking"
                  fill
                  className="w-full h-full object-cover"
                  priority
                />
              </div>
            </div>
          </div>
        </div>

        <div className="absolute inset-0 bg-black/10"></div>
        <Divider />

        <div className="relative z-10 flex h-full items-end justify-start pb-16 md:pb-24">
          <div className="container mx-auto px-4 text-left">
            <h1 className="text-5xl md:text-6xl font-bold text-light mb-10">
              Fastpacking Gears and Equipments
            </h1>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-2 md:px-4 py-16">
        <div className="container mx-auto mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-brand mb-6">
              The Art of Fastpacking
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-brand to-accent mx-auto mb-8"></div>
          </div>

          <Card className="p-8 mb-8 border-0 shadow-lg bg-gradient-to-br from-white to-secondary/30">
            <CardContent className="p-0">
              <p className="text-lg text-foreground/80 leading-relaxed mb-6">
                The main concept of fastpacking is to carry a lighter bag that helps you travel further distances in less time. Fastpacking gear enables trekkers to run and walk to cover multiple days of terrain efficiently.
              </p>
              <p className="text-lg text-foreground/80 leading-relaxed">
                By fitting all necessary supplies into a lightweight backpack, you can travel faster and cover greater distances, enabling you to explore more without needing to return to a fixed base each night.
              </p>
            </CardContent>
          </Card>

          <Card className="p-8 border-0 shadow-lg bg-gradient-to-br from-accent/5 to-brand/5">
            <CardContent className="p-0">
              <h3 className="text-2xl font-bold text-brand mb-4">Pack Like a Pro</h3>
              <p className="text-lg text-foreground/80 leading-relaxed">
                When fastpacking, keeping your gear well-organized is essential. Being aware of what you have and where it&apos;s stored, along with packing efficiently, simplifies everything. Use clear, waterproof zip-lock bags for equipment and dry bags for clothes and sleeping gear.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="">
          <h2 className="text-3xl font-bold text-brand mb-8 text-start">
            Fastpacking Gears and Equipments
          </h2>
          <p className="text-gray-600 text-start mb-12">
            Essential fastpacking gears and equipment you should carry are mentioned below:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {gearItems.map((item, index) => (
              <div
                key={index}
                className="bg-white p-1 md:p-6 text-center border-b border-gray-200 pb-8 md:pb-6"
              >
                <div className="w-44 h-44 mx-auto">
                  <Image
                    src={item.image}
                    alt={item.title}
                    width={300}
                    height={300}
                  />
                </div>
                <h3 className="text-lg font-semibold text-brand mb-3">
                  {item.title}
                </h3>
                <p className="text-sm text-dark/80 leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FastpackingGuide;