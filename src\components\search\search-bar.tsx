"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

export default function SearchBar({ defaultQuery = "" }: { defaultQuery?: string }) {
    const router = useRouter();
    const sp = useSearchParams();
    const [q, setQ] = useState(defaultQuery);
    const [field, setField] = useState(sp.get("field") ?? "name");

    useEffect(() => {
        setField(sp.get("field") ?? "name");
    }, [sp]);

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const query = q.trim();
        if (!query) return;
        router.push(`/search?query=${encodeURIComponent(query)}`);
    };

    return (
        <form onSubmit={onSubmit} className="flex flex-col md:flex-row gap-2 w-full md:w-[500px]">
            <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                    value={q}
                    onChange={(e) => setQ(e.target.value)}
                    placeholder={`Search in ${field}… (supports *)`}
                    className="pl-9"
                    spellCheck={false}
                />
            </div>
            <Button
                type="submit"
                className="text-white"
                disabled={!q.trim()}
            >
                Search
            </Button>
        </form>
    );
}
