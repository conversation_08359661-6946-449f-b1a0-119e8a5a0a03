import React from 'react'
import Image from 'next/image';

const MapandchartDhorpatan = () => {
    return (
        <div className="mt-12">
            <div className="relative mb-2">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-800 py-8 relative">
                    Dhorpatan Trek Map and Chart
                </h2>
                <div className="absolute bottom-6 w-15 rounded-full h-1 bg-brand"></div>
            </div>

            <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-8">
                <div className="">
                    <div className="relative w-full  h-70 md:h-[500px] rounded-lg overflow-hidden bg-gray-50">
                        <Image
                            src="/images/map/dhorpatan-map.webp"
                            alt="Annapurna Circuit Route Map showing the complete fastpacking trail with key landmarks, villages, and elevation points"
                            fill
                            className="object-fit"
                        />
                    </div>
                </div>
            </div>

            <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                <div className="p-6">
                    <div className="relative w-full h-70 md:h-[500px] rounded-lg overflow-hidden bg-gray-50">
                        <Image
                            src="/images/map/dhorpatan-chart.webp"
                            alt="Annapurna Circuit altitude profile chart showing elevation changes from Kathmandu to Pokhara via Thorung La Pass"
                            fill
                            className="object-fit"
                        />
                    </div>
                </div>
            </div>
        </div>)
}

export default MapandchartDhorpatan