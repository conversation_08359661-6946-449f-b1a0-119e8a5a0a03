import { IActivity } from '@/types/package';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

const fetchActivities = async (): Promise<IApiResponse<IActivity[]>> => {
  const response = await fetch('https://api.trailandtreknepal.com/activity', {
    mode: 'cors',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch activities');
  }

  return data;
};

const fetchActivitiesByRegionSlug = async (
  regionSlug: string
): Promise<IApiResponse<IActivity[]>> => {
  const response = await fetch(
    `https://api.trailandtreknepal.com/activity/region/${regionSlug}`,
    {
      mode: 'cors',
    }
  );
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch activities');
  }

  return data;
};

export const useActivities = () => {
  return useQuery({
    queryKey: ['activities'],
    queryFn: fetchActivities,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

export const useActivitiesByRegionSlug = (regionSlug: string) => {
  return useQuery({
    queryKey: ['activities', regionSlug],
    queryFn: () => fetchActivitiesByRegionSlug(regionSlug),
    enabled: !!regionSlug,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
