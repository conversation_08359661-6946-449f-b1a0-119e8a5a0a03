import React from 'react'
import { Map } from 'lucide-react';
import Image from 'next/image';

const MapandchartLangtang = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <Map size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-brand">Langtang Valley Route Map and Chart</h2>
            </div>

            <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-8">
                <div className="">
                    <div className="relative w-full  h-70 md:h-[500px] rounded-lg overflow-hidden bg-gray-50">
                        <Image
                            src="/images/map/langtang-map.webp"
                            alt="Annapurna Circuit Route Map showing the complete fastpacking trail with key landmarks, villages, and elevation points"
                            fill
                            className="object-fit"
                        />
                    </div>
                </div>
            </div>

            <div className="bg-light rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                <div className="p-6">
                    <div className="relative w-full h-70 md:h-[500px] rounded-lg overflow-hidden bg-gray-50">
                        <Image
                            src="/images/map/langtang-chart.webp"
                            alt="Annapurna Circuit altitude profile chart showing elevation changes from Kathmandu to Pokhara via Thorung La Pass"
                            fill
                            className="object-fit"
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default MapandchartLangtang