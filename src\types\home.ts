
export interface IHome {
  id: string;
  createdAt: string;
  updatedAt: string;
  overview: IOverview;
  adventure: IHomeAdventure;
  experience: IHomeExperience;
  hiking: IHomeHiking;
  review: IHomeReview;
  featuredpackages: IFeaturedPackage[];
  tailoredAdventure: IHomeTailoredAdventure;
  videoTestimonial: IHomeVideoTestimonial;
  hero: IHomeHero;
  seo: {
    structuredData: any;
  };
}

export interface IFeaturedPackage {
  id: string;
  name: string;
  duration: string;
  groupSize: string;
  price: string;
  discountPrice: string;
  bookingLink: string;
  overviewDescription: string;
  thumbnail: string;
  tripOftheMonth: boolean;
  published: boolean;
}

export interface IHomeAdventure {
  id: string;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeExperience {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  features: IHomeExperienceFeature[];
}

export interface IHomeExperienceFeature {
  id: string;
  homeExperienceId: string;
  title: string;
  subtitle: string;
  createdAt: string;
  updatedAt: string;
}

export interface IOverview {
  id: string;
  heading: string;
  description: string;
  linkUrl: string;
  linkLabel: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  points: IHomeOverviewPoints[];
}

export interface IHomeOverviewPoints {
  id: string;
  title: string;
  icon: string;
  homeOverviewId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeHiking {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  areas: IHomeHikingArea[];
}

export interface IHomeHikingArea {
  id: string;
  homeHikingId: string;
  image: string;
  title: string;
  subtitle: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeReview {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  reviews: IHomeReviewItem[];
}

export interface IHomeReviewItem {
  id: string;
  homeReviewId: string;
  image: string;
  quote: string;
  name: string;
  designation: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeTailoredAdventure {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  features: IHomeTailoredAdventureFeature[];
}

export interface IHomeTailoredAdventureFeature {
  id: string;
  homeTailoredAdventureId: string;
  title: string;
  subtitle: string;
  linkLabel: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeVideoTestimonial {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  testimonials: IHomeVideoTestimonialItem[];
}

export interface IHomeVideoTestimonialItem {
  id: string;
  homeVideoTestimonialId: string;
  youtubeThumbnail: string;
  youtubeUrl: string;
  title: string;
  destination: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeHero {
  id: string;
  videoUrl: string;
  youtubeUrl: string;
  titles: string[];
  subtitles: string[];
  images: string[];
  homeId: string;
  createdAt: string;
  updatedAt: string;
}
