import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { MapPin, Clock, Navigation, Car } from 'lucide-react';
import React from 'react'

const ItineraryEverestBaseCamp = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <MapPin size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-brand">Everest Base Camp Fastpacking Itinerary</h2>
            </div>

            <Accordion type="multiple" className="space-y-4">
                <AccordionItem value="day-1">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">1</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Flight from Kathmandu to Lukla
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Flight Duration: <strong>30 minutes</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Distance: <strong>134 km</strong></span>
                                </div>
                            </div>

                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '10%' }}></div>
                            </div>

                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Kathmandu: 1,324 m</div>
                                    <div>Lukla: 2,850 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-2">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">2</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Lukla to Namche Bazaar via Phakding
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>13.5 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6.5-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '20%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-brand/80">
                                    <div>Lukla: 2,860 m</div>
                                    <div>Phakding: 2,652 m</div>
                                    <div>Namche Bazaar: 3,440 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-3">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">3</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Namche Bazaar to Tengboche via Everest View Hotel
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>10.5 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>4.5-5 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '30%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-brand/80">
                                    <div>Namche Bazaar: 3,440 m</div>
                                    <div>Everest View Hotel: 3,880 m</div>
                                    <div>Tengboche: 3,860 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-4">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">4</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Tengboche to Lobuche via Dingboche
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>13.3 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '40%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-brand/80">
                                    <div>Tengboche: 3,860 m</div>
                                    <div>Dingboche: 4,410 m</div>
                                    <div>Lobuche: 4,910 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-5">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">5</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Lobuche to Gorakshep
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>4.3 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>1.5-2 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '50%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Lobuche: 4,910 m</div>
                                    <div>Gorakshep: 5,181 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-6">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">6</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Gorakshep to Everest Base Camp and back
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>8.6 km round trip</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>3-4 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '60%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Gorakshep: 5,181 m</div>
                                    <div>EBC: 5,364 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-7">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">7</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Gorakshep to Kala Patthar and back to Tengboche via Pheriche
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>14.5 km total</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>7-8 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '70%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-3 gap-2 text-sm text-brand/80">
                                    <div>Gorakshep: 5,181 m</div>
                                    <div>Kala Patthar: 5,644 m</div>
                                    <div>Pheriche: 4,210 m</div>
                                    <div>Tengboche: 3,860 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-8">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">8</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Tengboche to Lukla
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>17.1 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>7-8 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '80%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Tengboche: 3,860 m</div>
                                    <div>Lukla: 2,850 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="day-9">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">9</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Flight from Lukla to Kathmandu
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Flight Duration: <strong>30 minutes</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Distance: <strong>134 km</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Lukla: 2,850 m</div>
                                    <div>Kathmandu: 1,324 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
}

export default ItineraryEverestBaseCamp