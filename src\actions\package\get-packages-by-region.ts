import { IApiResponse } from '@/types/response';
import { IPackage } from '@/types/package';

export const revalidate = 0;

export default async function getPackagesByRegionSlug(slug: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/region-slug/${slug}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}

export async function getPackagesByRegionId(regionId: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/region/${regionId}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}



export async function getPackagesByRegionSlugWithPagination(slug: string) {
  let allPackages: IPackage[] = [];
  let currentPage = 1;
  let lastPage = 1;

  do {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/package/region-slug/${slug}?sort=createdAt%3Adesc&page=${currentPage}`,
      { cache: 'no-cache' }
    );

    const data: IApiResponse<IPackage[]> & { meta?: any } = await res.json();

    if (!data.success) break;

    allPackages = [...allPackages, ...(data.data ?? [])];
    lastPage = data.meta?.lastPage ?? 1;
    currentPage++;
  } while (currentPage <= lastPage);

  return {
    success: true,
    data: allPackages,
  };
}

