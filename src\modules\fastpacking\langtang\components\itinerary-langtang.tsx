import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { MapPin, Clock, Navigation, Car } from 'lucide-react';
import React from 'react'

const ItineraryLangtang = () => {
    return (
        <div className="mt-12">
            <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                    <MapPin size={16} className="text-light" />
                </div>
                <h2 className="text-3xl font-bold text-brand">Langtang Valley Trek Itinerary</h2>
            </div>

            <Accordion type="multiple" className="space-y-4">
                {/* Day 1 */}
                <AccordionItem value="day-1">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">1</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Kathmandu to Syabrubesi
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Drive Duration: <strong>7-8 hours</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Distance: <strong>122 km</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '15%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Kathmandu: 1,400 m</div>
                                    <div>Syabrubesi: 1,503 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 2 */}
                <AccordionItem value="day-2">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">2</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Syabrubesi to Langtang
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>21 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '30%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Syabrubesi: 1,803 m</div>
                                    <div>Langtang: 3,430 m</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 3 */}
                <AccordionItem value="day-3">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">3</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Langtang to Kyanjin Ri Exploration
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>12 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>6-7 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '45%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Langtang: 3,430 m</div>
                                    <div>Kyanjin Ri: 4,400 m</div>
                                    <div>Kyanjin Ri II: 4,773 m</div>
                                    <div>Kyanjin: 3,830 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Hike to both Kyanjin Ri viewpoints for spectacular mountain views before returning to Kyanjin village.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 4 */}
                <AccordionItem value="day-4">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">4</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Kyanjin to Tsergo Ri and Langtang
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>15-17 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '60%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Kyanjin: 3,830 m</div>
                                    <div>Tsergo Ri: 5,033 m</div>
                                    <div>Langtang: 3,430 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Challenging hike to Tsergo Ri (5,033m) for panoramic views before descending back to Langtang village.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 5 */}
                <AccordionItem value="day-5">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">5</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Langtang to Syabrubesi
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Fastpacking Distance: <strong>25 km</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Clock size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Duration: <strong>5-6 hours</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '75%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Langtang: 3,430 m</div>
                                    <div>Syabrubesi: 1,503 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Long descent through beautiful forests and villages back to Syabrubesi.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Day 6 */}
                <AccordionItem value="day-6">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <div className="flex items-center gap-4 w-full">
                            <div className="flex flex-col items-center">
                                <span className="text-sm text-brand font-medium">Day</span>
                                <span className="text-2xl font-bold text-brand">6</span>
                            </div>
                            <div className="flex-1 text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Return to Kathmandu
                                </h3>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-4">
                            <div className="flex gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                    <Car size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Drive Duration: <strong>7-8 hours</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Navigation size={16} className="text-dark/80" />
                                    <span className="text-sm text-dark/90">Distance: <strong>122 km</strong></span>
                                </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-brand h-2 rounded-full" style={{ width: '100%' }}></div>
                            </div>
                            <div className="bg-brand/5 border border-brand rounded-lg p-4">
                                <h4 className="font-semibold text-brand/80 mb-2">Altitudes:</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm text-brand/80">
                                    <div>Syabrubesi: 1,503 m</div>
                                    <div>Kathmandu: 1,400 m</div>
                                </div>
                            </div>
                            <p className="text-dark/85">
                                Scenic drive back to Kathmandu, completing the Langtang Valley trek.
                            </p>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
}

export default ItineraryLangtang