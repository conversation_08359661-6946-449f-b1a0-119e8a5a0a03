'use client'

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Package } from 'lucide-react';

interface Package {
  id: string;
  title: string;
  image: string;
  link: string;
}

interface OtherPackagesProps {
  packages?: Package[];
  title?: string;
}

const OtherPackages: React.FC<OtherPackagesProps> = ({
  title = "OTHER PACKAGES",
  packages = [
    {
      id: "1",
      title: "Manaslu Circuit Tsum Valley Trek",
      image: "/images/footer-image.png",
      link: "/treks/manaslu-circuit-tsum-valley"
    },
    {
      id: "2",
      title: "Annapurna Circuit Trek",
      image: "/images/footer-image.png",
      link: "/treks/annapurna-circuit"
    },
    {
      id: "3",
      title: "Annapurna Base Camp Trek - 13 Days",
      image: "/images/footer-image.png",
      link: "/treks/annapurna-base-camp-13-days"
    },
    {
      id: "4",
      title: "Lobuche Peak Climbing",
      image: "/images/footer-image.png",
      link: "/treks/lobuche-peak-climbing"
    },
    {
      id: "5",
      title: "Kapuche Glacier Lake Trek",
      image: "/images/footer-image.png",
      link: "/treks/kapuche-glacier-lake"
    }
  ]
}) => {

  return (
    <div className="bg-light border border-dark/80 rounded-sm overflow-hidden shadow-sm">
      <div className="relative flex justify-center">
        <div className="bg-red text-light inline-flex items-center space-x-2 py-2 px-4">
          <Package size={16} className="text-light" />
          <span className="font-semibold text-sm">{title}</span>
        </div>
      </div>

      <div className="p-4">
        <div className="space-y-3">
          {packages.map((pkg) => (
            <Link
              key={pkg.id}
              href={pkg.link}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
            >
              <div className="w-12 h-12 relative flex-shrink-0 rounded-lg overflow-hidden">
                <Image
                  src={pkg.image}
                  alt={pkg.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-200"
                />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-dark/60 group-hover:text-brand transition-colors duration-200 line-clamp-2">
                  {pkg.title}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OtherPackages;